name: Deploy Post Confirmation Lambda - PROD

on:
  push:
    branches: [ release ]
    paths:
      - 'infrastructure/lambda/postConfirmationLambda/**'

jobs:
  deploy_lambda:
    runs-on: ubuntu-latest  #Through which Server OS we need to Work (type of machine to run the job on)
    environment: production
    steps:
    #Using versioned actions 
      - uses: actions/checkout@v2  # --> Reference a specific version
      - uses: actions/setup-node@v2 # --> To Setup the Server With Node Env
        with:  
          node-version: '16' #--> Specify the Version of the Node
          
      - name: Configure AWS Credentials  
        uses: aws-actions/configure-aws-credentials@v1 #--> Setup the Credential for the AWS cli
        with:
        # Created the Secrets Under the Repo only with These Variables
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }} 
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1 #--> Define Region of the AWS-CLI 
          
      - name: Build Lambda
        run: |
          cd infrastructure/lambda/postConfirmationLambda && yarn install && yarn build
          
      - name: ZIP Lambda and Deploy
        run: |
          aws lambda update-function-code --function-name=postConfirmationLambda-eg-prod --zip-file=fileb://infrastructure/lambda/postConfirmationLambda/post-confirmation-lambda.zip

      - name: Remove Lambda ZIP
        run: |
          cd infrastructure/lambda/postConfirmationLambda && rm ./post-confirmation-lambda.zip
            
        
