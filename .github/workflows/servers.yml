name: Deploy Servers to ECR - DEV

on:
 
  push:
    branches: [ main ]
    paths: 
     - 'packages/**'

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest
    environment: development

   
    steps:

    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-central-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push user server image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: easygarage-user-server
        IMAGE_TAG: easygarage-user-server
      run: |
        cd packages/user-server
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --target prod .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        cd ../../
    
    - name: Build, tag, and push gateway server image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: easygarage-gateway-server
        IMAGE_TAG: easygarage-gateway-server
        
      run: |
        cd packages/gateway-server
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --target prod .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        cd ../../