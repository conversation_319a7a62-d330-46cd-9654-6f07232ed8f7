version: '3.8'

x-node-build: &node-build
    dockerfile: Dockerfile
    target: dev

services:
    user-server:
        build:
            <<: *node-build
            context: ./packages/user-server
        image: user-server
        restart: on-failure
        ports:
            - 4001:4001
        volumes:
            - ./packages/user-server:/usr/src/app:rw
            - /usr/src/app/node_modules
        networks:
            - easygarage-backend_local-network

    appointments-server:
        build:
            <<: *node-build
            context: ./packages/appointments-server
        image: appointments-server
        restart: on-failure
        ports:
            - 4002:4002
        volumes:
            - ./packages/appointments-server:/usr/src/app:rw
            - /usr/src/app/node_modules
        networks:
            - easygarage-backend_local-network

    gateway-server:
        build:
            <<: *node-build
            context: ./packages/gateway-server
        image: gateway-server
        restart: on-failure
        ports:
            - 4000:4000
        volumes:
            - ./packages/gateway-server:/usr/src/app:rw
            - /usr/src/app/node_modules
        networks:
            - easygarage-backend_local-network

networks:
    easygarage-backend_local-network:
        external: true
