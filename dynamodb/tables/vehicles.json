{"AttributeDefinitions": [{"AttributeName": "vehicle_id", "AttributeType": "S"}, {"AttributeName": "user_id", "AttributeType": "S"}], "TableName": "Vehicles", "KeySchema": [{"AttributeName": "vehicle_id", "KeyType": "HASH"}], "ProvisionedThroughput": {"ReadCapacityUnits": 3, "WriteCapacityUnits": 3}, "GlobalSecondaryIndexes": [{"IndexName": "idx_user", "KeySchema": [{"AttributeName": "user_id", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}, "ProvisionedThroughput": {"ReadCapacityUnits": 3, "WriteCapacityUnits": 3}}]}