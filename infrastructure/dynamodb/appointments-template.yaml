Parameters:
    Environment:
        Type: String

Resources:
    QuestionsTable:
        Type: AWS::DynamoDB::Table
        Properties:
            AttributeDefinitions:
                - AttributeName: 'appointment_id'
                  AttributeType: 'S'
                - AttributeName: 'client_id'
                  AttributeType: 'S'
                - AttributeName: 'company_id'
                  AttributeType: 'S'
                - AttributeName: 'date_time'
                  AttributeType: 'N'
            KeySchema:
                - AttributeName: 'appointment_id'
                  KeyType: 'HASH'
            GlobalSecondaryIndexes:
                - IndexName: 'idx_client'
                  KeySchema:
                      - AttributeName: 'client_id'
                        KeyType: 'HASH'
                      - AttributeName: 'date_time'
                        KeyType: 'RANGE'
                  Projection:
                      ProjectionType: 'ALL'
                  ProvisionedThroughput:
                      ReadCapacityUnits: 3
                      WriteCapacityUnits: 3
                - IndexName: 'idx_company'
                  KeySchema:
                      - AttributeName: 'company_id'
                        KeyType: 'HASH'
                      - AttributeName: 'date_time'
                        KeyType: 'RANGE'
                  Projection:
                      ProjectionType: 'ALL'
                  ProvisionedThroughput:
                      ReadCapacityUnits: 3
                      WriteCapacityUnits: 3
            ProvisionedThroughput:
                ReadCapacityUnits: 3
                WriteCapacityUnits: 3
            TimeToLiveSpecification:
                AttributeName: 'expire_date'
                Enabled: true
            TableName: !Sub appointments-${Environment}
