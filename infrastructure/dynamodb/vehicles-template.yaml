Parameters:
    Environment:
        Type: String

Resources:
    QuestionsTable:
        Type: AWS::DynamoDB::Table
        Properties:
            AttributeDefinitions:
                - AttributeName: 'vehicle_id'
                  AttributeType: 'S'
                - AttributeName: 'user_id'
                  AttributeType: 'S'
            KeySchema:
                - AttributeName: 'vehicle_id'
                  KeyType: 'HASH'
            GlobalSecondaryIndexes:
                - IndexName: 'idx_user'
                  KeySchema:
                      - AttributeName: 'user_id'
                        KeyType: 'HASH'
                  Projection:
                      ProjectionType: 'ALL'
                  ProvisionedThroughput:
                      ReadCapacityUnits: 3
                      WriteCapacityUnits: 3
            ProvisionedThroughput:
                ReadCapacityUnits: 3
                WriteCapacityUnits: 3
            TimeToLiveSpecification:
                AttributeName: 'expire_date'
                Enabled: true
            TableName: !Sub vehicles-${Environment}
