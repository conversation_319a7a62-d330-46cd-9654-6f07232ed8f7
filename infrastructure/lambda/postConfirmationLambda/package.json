{"name": "post-confirmation-lambda", "version": "1.0.0", "description": "", "main": "src/index.ts", "license": "MIT", "scripts": {"copy": "cp package.json dist/ && cp yarn.lock dist/", "prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json && yarn copy && cd dist && yarn install --frozen-lockfile && zip -r ../post-confirmation-lambda.zip .", "clean": "rm -rf dist/ && rm -rf post-confirmation-lambda.zip", "start": "ts-node src/index.ts", "start:dev": "nodemon src/index.ts", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "devDependencies": {"@types/aws-lambda": "^8.10.103", "@types/node": "^20.3.3", "typescript": "^4.8.3"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.259.0", "dotenv": "^16.0.2", "graphql": "^16.6.0", "graphql-request": "^5.2.0", "ts-node": "^10.9.1"}}