import dotenv from 'dotenv';
dotenv.config();

const requiredEnvVars = ['NODE_ENV', 'APOLLO_INTERNAL_USER_ID', 'APOLLO_INTERNAL_SECRET', 'GATEWAY_URL', 'USER_POOL_ID', 'AWS_REGION'];

const missingVars = requiredEnvVars.filter((variable) => process.env[variable] === undefined);

if (missingVars.length > 0) {
    console.log('Missing env variables: ', missingVars);
}

const { NODE_ENV, APOLLO_INTERNAL_USER_ID, GATEWAY_URL, APOLLO_INTERNAL_SECRET, USER_POOL_ID, AWS_REGION } = process.env;

export default {
    NODE_ENV,
    APOLLO_INTERNAL_USER_ID,
    GATEWAY_URL,
    APOLLO_INTERNAL_SECRET,
    USER_POOL_ID,
    AWS_REGION,
};
