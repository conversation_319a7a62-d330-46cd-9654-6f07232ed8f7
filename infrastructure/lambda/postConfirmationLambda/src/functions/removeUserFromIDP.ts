import { CognitoIdentityProviderClient, AdminDeleteUserCommand } from '@aws-sdk/client-cognito-identity-provider';

import env from '../env';

export const removeUserFromIDP = async (username: string) => {
    const client = new CognitoIdentityProviderClient({ region: env.AWS_REGION });

    const adminDeleteUserCommandConfig = { UserPoolId: env.USER_POOL_ID, Username: username };

    const adminDeleteUserCommandResult = await client.send(new AdminDeleteUserCommand(adminDeleteUserCommandConfig));

    return adminDeleteUserCommandResult;
};
