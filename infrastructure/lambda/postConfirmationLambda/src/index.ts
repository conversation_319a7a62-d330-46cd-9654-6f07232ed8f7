import { putUser } from './services/user/user';

export const handler = async (event: any) => {
    const userId = event.request.userAttributes?.sub;
    const email = event.request.userAttributes?.email;

    if (userId && email && event.triggerSource === 'PostConfirmation_ConfirmSignUp') {
        const result = await putUser({
            userId,
            email,
        });
        if (result.status === 201) {
            console.info('Successfully added user with id ', userId);
            // Lambda expects event to be returned on success
            return event;
        }
        return result;
    }
    return { status: 500, message: 'PostConfirmationTriggerError: Could not verify user' };
};
