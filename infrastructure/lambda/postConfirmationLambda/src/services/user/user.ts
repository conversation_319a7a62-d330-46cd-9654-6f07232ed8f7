import request from 'graphql-request';
import env from '../../env';
import { removeUserFromIDP } from '../../functions/removeUserFromIDP';
import { ADD_USER } from './queries';

const { GATEWAY_URL = 'http://localhost:4000/graphql', APOLLO_INTERNAL_USER_ID = 'APOLLO-INTERNAL', APOLLO_INTERNAL_SECRET = 'secret' } = env;

export const putUser = (user) => {
    const recursivePutUser = async (retries: number, error?: string) => {
        let retriesCpy = retries;
        if (retriesCpy === 0) {
            await removeUserFromIDP(user.username);
            const message = `Failed to put user in DB: ${error}, removing user from IDP..`;
            console.error(message);
            return {
                status: 400,
                message,
            };
        }
        try {
            const headers = {
                'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
                'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
            };

            const response: any = await request(GATEWAY_URL, ADD_USER, { user: user }, headers);

            if (response?.addUser.status === 500 || !response) {
                throw new Error(`Failed to add user: ${response?.addUser.message}`);
            }

            return response?.addUser;
        } catch (err) {
            retriesCpy -= 1;
            console.log(retriesCpy > 0 ? `Repeating put user operation...Retries left: ${retriesCpy}` : `Maximum retries exceeded ${err}`);
            return recursivePutUser(retriesCpy, err);
        }
    };

    return recursivePutUser(2);
};
