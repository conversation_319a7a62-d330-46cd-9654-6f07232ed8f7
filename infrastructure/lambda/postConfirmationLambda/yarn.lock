# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@aws-crypto/ie11-detection@npm:^3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/ie11-detection@npm:3.0.0"
  dependencies:
    tslib: ^1.11.1
  checksum: 299b2ddd46eddac1f2d54d91386ceb37af81aef8a800669281c73d634ed17fd855dcfb8b3157f2879344b93a2666a6d602550eb84b71e4d7868100ad6da8f803
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/sha256-browser@npm:3.0.0"
  dependencies:
    "@aws-crypto/ie11-detection": ^3.0.0
    "@aws-crypto/sha256-js": ^3.0.0
    "@aws-crypto/supports-web-crypto": ^3.0.0
    "@aws-crypto/util": ^3.0.0
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-locate-window": ^3.0.0
    "@aws-sdk/util-utf8-browser": ^3.0.0
    tslib: ^1.11.1
  checksum: ca89456bf508db2e08060a7f656460db97ac9a15b11e39d6fa7665e2b156508a1758695bff8e82d0a00178d6ac5c36f35eb4bcfac2e48621265224ca14a19bd2
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:3.0.0, @aws-crypto/sha256-js@npm:^3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/sha256-js@npm:3.0.0"
  dependencies:
    "@aws-crypto/util": ^3.0.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^1.11.1
  checksum: 644ded32ea310237811afae873d3c7320739cb6f6cc39dced9c94801379e68e5ee2cca0c34f0384793fa9e750a7e0a5e2468f95754bd08e6fd72ab833c8fe23c
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/supports-web-crypto@npm:3.0.0"
  dependencies:
    tslib: ^1.11.1
  checksum: 35479a1558db9e9a521df6877a99f95670e972c602f2a0349303477e5d638a5baf569fb037c853710e382086e6fd77e8ed58d3fb9b49f6e1186a9d26ce7be006
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/util@npm:3.0.0"
  dependencies:
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-utf8-browser": ^3.0.0
    tslib: ^1.11.1
  checksum: d29d5545048721aae3d60b236708535059733019a105f8a64b4e4a8eab7cf8dde1546dc56bff7de20d36140a4d1f0f4693e639c5732a7059273a7b1e56354776
  languageName: node
  linkType: hard

"@aws-sdk/abort-controller@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/abort-controller@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: fca54be92f6cbfe8f0675aba19a50960376506a4c9aaa9c44220e8c29a7b3c8c74198537f831c1d4818ec686df97af86d055d8f3265c3a6fde56bbf593241c47
  languageName: node
  linkType: hard

"@aws-sdk/client-cognito-identity-provider@npm:^3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/client-cognito-identity-provider@npm:3.259.0"
  dependencies:
    "@aws-crypto/sha256-browser": 3.0.0
    "@aws-crypto/sha256-js": 3.0.0
    "@aws-sdk/client-sts": 3.259.0
    "@aws-sdk/config-resolver": 3.259.0
    "@aws-sdk/credential-provider-node": 3.259.0
    "@aws-sdk/fetch-http-handler": 3.257.0
    "@aws-sdk/hash-node": 3.257.0
    "@aws-sdk/invalid-dependency": 3.257.0
    "@aws-sdk/middleware-content-length": 3.257.0
    "@aws-sdk/middleware-endpoint": 3.257.0
    "@aws-sdk/middleware-host-header": 3.257.0
    "@aws-sdk/middleware-logger": 3.257.0
    "@aws-sdk/middleware-recursion-detection": 3.257.0
    "@aws-sdk/middleware-retry": 3.259.0
    "@aws-sdk/middleware-serde": 3.257.0
    "@aws-sdk/middleware-signing": 3.257.0
    "@aws-sdk/middleware-stack": 3.257.0
    "@aws-sdk/middleware-user-agent": 3.257.0
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/node-http-handler": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/smithy-client": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    "@aws-sdk/util-base64": 3.208.0
    "@aws-sdk/util-body-length-browser": 3.188.0
    "@aws-sdk/util-body-length-node": 3.208.0
    "@aws-sdk/util-defaults-mode-browser": 3.257.0
    "@aws-sdk/util-defaults-mode-node": 3.259.0
    "@aws-sdk/util-endpoints": 3.257.0
    "@aws-sdk/util-retry": 3.257.0
    "@aws-sdk/util-user-agent-browser": 3.257.0
    "@aws-sdk/util-user-agent-node": 3.259.0
    "@aws-sdk/util-utf8": 3.254.0
    tslib: ^2.3.1
  checksum: fe0f037d00a271a0047f55f96d959cd317febdc572ad97a6ebc7063f83f8246527466f5b82b5dd38b2a6c1a7fd433dd974d8dc239075719cc3c21ed26315c831
  languageName: node
  linkType: hard

"@aws-sdk/client-sso-oidc@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/client-sso-oidc@npm:3.259.0"
  dependencies:
    "@aws-crypto/sha256-browser": 3.0.0
    "@aws-crypto/sha256-js": 3.0.0
    "@aws-sdk/config-resolver": 3.259.0
    "@aws-sdk/fetch-http-handler": 3.257.0
    "@aws-sdk/hash-node": 3.257.0
    "@aws-sdk/invalid-dependency": 3.257.0
    "@aws-sdk/middleware-content-length": 3.257.0
    "@aws-sdk/middleware-endpoint": 3.257.0
    "@aws-sdk/middleware-host-header": 3.257.0
    "@aws-sdk/middleware-logger": 3.257.0
    "@aws-sdk/middleware-recursion-detection": 3.257.0
    "@aws-sdk/middleware-retry": 3.259.0
    "@aws-sdk/middleware-serde": 3.257.0
    "@aws-sdk/middleware-stack": 3.257.0
    "@aws-sdk/middleware-user-agent": 3.257.0
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/node-http-handler": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/smithy-client": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    "@aws-sdk/util-base64": 3.208.0
    "@aws-sdk/util-body-length-browser": 3.188.0
    "@aws-sdk/util-body-length-node": 3.208.0
    "@aws-sdk/util-defaults-mode-browser": 3.257.0
    "@aws-sdk/util-defaults-mode-node": 3.259.0
    "@aws-sdk/util-endpoints": 3.257.0
    "@aws-sdk/util-retry": 3.257.0
    "@aws-sdk/util-user-agent-browser": 3.257.0
    "@aws-sdk/util-user-agent-node": 3.259.0
    "@aws-sdk/util-utf8": 3.254.0
    tslib: ^2.3.1
  checksum: a2e41f4b7dec1b986aadc9768551ef65c3db844431d26ef8e78e6824b66139abfae402403820728de79694f0002f968d098a92ca48f9755912d75b1caf69b0c9
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/client-sso@npm:3.259.0"
  dependencies:
    "@aws-crypto/sha256-browser": 3.0.0
    "@aws-crypto/sha256-js": 3.0.0
    "@aws-sdk/config-resolver": 3.259.0
    "@aws-sdk/fetch-http-handler": 3.257.0
    "@aws-sdk/hash-node": 3.257.0
    "@aws-sdk/invalid-dependency": 3.257.0
    "@aws-sdk/middleware-content-length": 3.257.0
    "@aws-sdk/middleware-endpoint": 3.257.0
    "@aws-sdk/middleware-host-header": 3.257.0
    "@aws-sdk/middleware-logger": 3.257.0
    "@aws-sdk/middleware-recursion-detection": 3.257.0
    "@aws-sdk/middleware-retry": 3.259.0
    "@aws-sdk/middleware-serde": 3.257.0
    "@aws-sdk/middleware-stack": 3.257.0
    "@aws-sdk/middleware-user-agent": 3.257.0
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/node-http-handler": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/smithy-client": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    "@aws-sdk/util-base64": 3.208.0
    "@aws-sdk/util-body-length-browser": 3.188.0
    "@aws-sdk/util-body-length-node": 3.208.0
    "@aws-sdk/util-defaults-mode-browser": 3.257.0
    "@aws-sdk/util-defaults-mode-node": 3.259.0
    "@aws-sdk/util-endpoints": 3.257.0
    "@aws-sdk/util-retry": 3.257.0
    "@aws-sdk/util-user-agent-browser": 3.257.0
    "@aws-sdk/util-user-agent-node": 3.259.0
    "@aws-sdk/util-utf8": 3.254.0
    tslib: ^2.3.1
  checksum: a4b8c1740c5dad36d4b477d1a028f32a72c882ed854504a830a65897c3615594dce516d2672e140cf18af1db16afaa7cb918a64f61dacc5f301d4072d7f59d05
  languageName: node
  linkType: hard

"@aws-sdk/client-sts@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/client-sts@npm:3.259.0"
  dependencies:
    "@aws-crypto/sha256-browser": 3.0.0
    "@aws-crypto/sha256-js": 3.0.0
    "@aws-sdk/config-resolver": 3.259.0
    "@aws-sdk/credential-provider-node": 3.259.0
    "@aws-sdk/fetch-http-handler": 3.257.0
    "@aws-sdk/hash-node": 3.257.0
    "@aws-sdk/invalid-dependency": 3.257.0
    "@aws-sdk/middleware-content-length": 3.257.0
    "@aws-sdk/middleware-endpoint": 3.257.0
    "@aws-sdk/middleware-host-header": 3.257.0
    "@aws-sdk/middleware-logger": 3.257.0
    "@aws-sdk/middleware-recursion-detection": 3.257.0
    "@aws-sdk/middleware-retry": 3.259.0
    "@aws-sdk/middleware-sdk-sts": 3.257.0
    "@aws-sdk/middleware-serde": 3.257.0
    "@aws-sdk/middleware-signing": 3.257.0
    "@aws-sdk/middleware-stack": 3.257.0
    "@aws-sdk/middleware-user-agent": 3.257.0
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/node-http-handler": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/smithy-client": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    "@aws-sdk/util-base64": 3.208.0
    "@aws-sdk/util-body-length-browser": 3.188.0
    "@aws-sdk/util-body-length-node": 3.208.0
    "@aws-sdk/util-defaults-mode-browser": 3.257.0
    "@aws-sdk/util-defaults-mode-node": 3.259.0
    "@aws-sdk/util-endpoints": 3.257.0
    "@aws-sdk/util-retry": 3.257.0
    "@aws-sdk/util-user-agent-browser": 3.257.0
    "@aws-sdk/util-user-agent-node": 3.259.0
    "@aws-sdk/util-utf8": 3.254.0
    fast-xml-parser: 4.0.11
    tslib: ^2.3.1
  checksum: fce3415bb1967ec2aaee139385765e9e556624c21935ce0e36ebc70c569630998ef8c0c9f1628df482b3aaf8f775094a332cd968b2c62e94ebb16227a5b3bd8d
  languageName: node
  linkType: hard

"@aws-sdk/config-resolver@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/config-resolver@npm:3.259.0"
  dependencies:
    "@aws-sdk/signature-v4": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-config-provider": 3.208.0
    "@aws-sdk/util-middleware": 3.257.0
    tslib: ^2.3.1
  checksum: c461dfd62291c8dbc3394d586aa44a14326c0a9f02e4cc84dfa6569c6003890d21343aaa28a067337949837f7e2c2e3533465e3e2e5f2ee3e42bb30cc69de0d4
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.257.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: e268c46ff374eddb5cd5bef62a8b969a5bfd5202e15122ecbd2baa7882c99f75c2e2b6dfc0c294fcfc331e9e2028620c827278f1eec6f7158c30f079ae67dc5f
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-imds@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/credential-provider-imds@npm:3.259.0"
  dependencies:
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    tslib: ^2.3.1
  checksum: 7149bb155e9f169f6d9a4e17d16c683b5eed6f71871230f1c786b884f22fe438b3f29e6179a5538ad540f025d2eb3cfa1a4d9a1bd1e1ec495a539f8d164903da
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.259.0"
  dependencies:
    "@aws-sdk/credential-provider-env": 3.257.0
    "@aws-sdk/credential-provider-imds": 3.259.0
    "@aws-sdk/credential-provider-process": 3.257.0
    "@aws-sdk/credential-provider-sso": 3.259.0
    "@aws-sdk/credential-provider-web-identity": 3.257.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 01b0de0f9b78565d93ee575f3bdb54c4fde46527244ccc3a6bef377561f40c07682e6526566938b43520efeb4268e7f3ea41c603f887573025c9aedb1d44882b
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.259.0"
  dependencies:
    "@aws-sdk/credential-provider-env": 3.257.0
    "@aws-sdk/credential-provider-imds": 3.259.0
    "@aws-sdk/credential-provider-ini": 3.259.0
    "@aws-sdk/credential-provider-process": 3.257.0
    "@aws-sdk/credential-provider-sso": 3.259.0
    "@aws-sdk/credential-provider-web-identity": 3.257.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 2a289c555d65a7117b5efd41bd3e3806a8f51d07fa23e3d599ef74f56461f19f9fbd8c87866ddb29358d3c8aa615daf28f5fde6ecac041ea78971583f9a17c19
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.257.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: cb34261ae87463245e4cc3ddab306201c52a8fbcfedc122ae9782bdf9263ab8fabc33cde028def52d069452a842338033829aaecfec2d0fc00547513a76c9b6f
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.259.0"
  dependencies:
    "@aws-sdk/client-sso": 3.259.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/token-providers": 3.259.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: cff9f90b17a08424835c43e3f16bcb27fd53233568c13e2425ab7203e3d87bacb9082b6c8d78d651146c149c8fff2eda3f2526ff45a233bc697024f823cddc14
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.257.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 2d3ee4975af680337cd6a9f25b6eb874201f4787ddc0217f80cb7ebb36c70424108fa8c04e5a9f0f8c2130d06685a3de3938c41b8c78e238b9faeb348c0cd361
  languageName: node
  linkType: hard

"@aws-sdk/fetch-http-handler@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/fetch-http-handler@npm:3.257.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/querystring-builder": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-base64": 3.208.0
    tslib: ^2.3.1
  checksum: de25f542db072225c23403254be7ff4e642f12e348eb703cdb30a5e9b0ac5e4f18930947490f6bb27a31b8f8a462abfdd7b9344b16e41b6b52e1fe969ac95396
  languageName: node
  linkType: hard

"@aws-sdk/hash-node@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/hash-node@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-buffer-from": 3.208.0
    "@aws-sdk/util-utf8": 3.254.0
    tslib: ^2.3.1
  checksum: 26f8f2fca7ad13a1dc64753ee1ef85014eb89adfef9f500f5003e479807c5f2ec6ee185ae8e88d6bcb68e26512702fd6886fe211486531e4388e40fd003678a5
  languageName: node
  linkType: hard

"@aws-sdk/invalid-dependency@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/invalid-dependency@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: fe99f546cc041a9ccc8b29654fd3e8e466aef41697d4c69f4afc1aab1f1f83f2e7f0b9e0e20b3fe23843a01008902f00aeeb24cf26b7df0594c75a4ebf3bb3f3
  languageName: node
  linkType: hard

"@aws-sdk/is-array-buffer@npm:3.201.0":
  version: 3.201.0
  resolution: "@aws-sdk/is-array-buffer@npm:3.201.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 295450b417a9ab0b734050afff6c53aaed8a33dccd3ede60bf67fdec21f675d14ab8edc24f4e1d12aa4e99f9ccaf794aaaaff270c296c1ee38f73ea7ba7f59ce
  languageName: node
  linkType: hard

"@aws-sdk/middleware-content-length@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-content-length@npm:3.257.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 4f6129653b98185624c66bf2114417d492ec3cf91063da4cbce0e07ad3b1831766ff684491f0c14f92c8b9b39599a9781ca883f2d5ee42c3d38b273b6664f361
  languageName: node
  linkType: hard

"@aws-sdk/middleware-endpoint@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-endpoint@npm:3.257.0"
  dependencies:
    "@aws-sdk/middleware-serde": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/signature-v4": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/url-parser": 3.257.0
    "@aws-sdk/util-config-provider": 3.208.0
    "@aws-sdk/util-middleware": 3.257.0
    tslib: ^2.3.1
  checksum: e709f6b5dc6bee4659caa7740847f53be359b215d307210f093629f5013f3d0521e060ef6f7797c82dd02a9ac0528b47581e6e19fb0e2f38aa2ff3a8f1b9abc0
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.257.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 426f6a6a9964ef993d4e7d8401162b62f758a7247872c6b58163d40ec8320738a84dbd2dbaccd008b24801642f6fd2d24b6af11331f00a204ed48e814f1e6e3c
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-logger@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 3b4ac39d4be50dde9b806500a50a6d2c4ab267f6e02e780fe70adf0d07f4026c09131e3f787d86833f4295987cbad6858839884d1c5f1ea56df729fc4a8d7d49
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.257.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: ce972140735fb040d00e1356409beac6dc5d9c477f975c73776de8294a91f81ac2a87e11c02581ff6c8b8c6f84e455e77a081291bbc536d22c9e128b07928fcd
  languageName: node
  linkType: hard

"@aws-sdk/middleware-retry@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/middleware-retry@npm:3.259.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/service-error-classification": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-middleware": 3.257.0
    "@aws-sdk/util-retry": 3.257.0
    tslib: ^2.3.1
    uuid: ^8.3.2
  checksum: 22ae9d575cafbc9c3806a10871e2b6749d12b8109bc15e8eabe82e24613dc60196c2ab48df62fcf39e3dae33319146a58d3983c51a68c6c1da89e941ac2d4efa
  languageName: node
  linkType: hard

"@aws-sdk/middleware-sdk-sts@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-sdk-sts@npm:3.257.0"
  dependencies:
    "@aws-sdk/middleware-signing": 3.257.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/signature-v4": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 9171f0fc8a9518b8344c58f26446c9b39a19d00b747f2ae02b92b022d8fdcdb65528f05e753999676b3c04bffa38d008a599d86c2e0fa65b2196cd9633ab4308
  languageName: node
  linkType: hard

"@aws-sdk/middleware-serde@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-serde@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: ceda3cb7bb7ceced0d5b69363dca51fa92b4f6defcd8299910eee0fe65d557871aa7777dade4abc64aa91347d038727fa65ff7ff0cc3d9716d0065dfd939c724
  languageName: node
  linkType: hard

"@aws-sdk/middleware-signing@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-signing@npm:3.257.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/signature-v4": 3.257.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-middleware": 3.257.0
    tslib: ^2.3.1
  checksum: dfa31f59675427f7aff83852f24e2393bd7d1295421d68c2bb060faca001ce385d7a1d7231c92c48e841e58581ceeffef0209c7c91b51b19361af29120342a50
  languageName: node
  linkType: hard

"@aws-sdk/middleware-stack@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-stack@npm:3.257.0"
  dependencies:
    tslib: ^2.3.1
  checksum: f7520d3e53d026f46c6504bcebef2a5732b724ab79079ccd2db0c052f6d4fd3cc4f06b951ff02723ef9ba46c6a0a0deca3282f152172ae7b1c3b2f493d35d584
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.257.0"
  dependencies:
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: b2fc45a734845d40d1e69bf7f0890613aad8357d4830f704c7ee6c1339ab51cbb0cef51920dc22718d29eb258fc1024f8c23cbc53a4f93b9b857f8bcacc442a1
  languageName: node
  linkType: hard

"@aws-sdk/node-config-provider@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/node-config-provider@npm:3.259.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 72e4818b1ab641d1aa3b85c7901f7d64ad0399eb384cda1c875b41a37021c2be9e68021e6b0aa3f7298d2d46017259ee10356f07c314b4bc40146a44f355135a
  languageName: node
  linkType: hard

"@aws-sdk/node-http-handler@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/node-http-handler@npm:3.257.0"
  dependencies:
    "@aws-sdk/abort-controller": 3.257.0
    "@aws-sdk/protocol-http": 3.257.0
    "@aws-sdk/querystring-builder": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 37531827bc9e0b590c67ce57b87315fab8639a5feff1cb6cda8effdf6ac2d2fbc8a645765f004b52b03ff39c896da88a090b0dbcf5cfcf5d3d65d0b995f628fb
  languageName: node
  linkType: hard

"@aws-sdk/property-provider@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/property-provider@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 066290e9b88bcbc7d6bace37482ce955c782386a6cd75dc789efeb368bc87d0c410d01fd6b25bf66918e3328c6ffe1942aa643f4fdd4fa604feee36af9114894
  languageName: node
  linkType: hard

"@aws-sdk/protocol-http@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/protocol-http@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: c51539e2f617d5d84e51ad9feeb93d51a310d808da18ceb53317aaf35af99bb8bea52eca532ab68d546b0ff5156b92cef5c3e98245f0b425795bd7910b608dad
  languageName: node
  linkType: hard

"@aws-sdk/querystring-builder@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/querystring-builder@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-uri-escape": 3.201.0
    tslib: ^2.3.1
  checksum: a66b07cd1c4af544c7c6f903a735d1e8359f7d3b5c74803fc63b16b854eafec21f4b1c8874761edeebc4c2341f1f0b72190f03e0a005e8fff7b619e30cd59896
  languageName: node
  linkType: hard

"@aws-sdk/querystring-parser@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/querystring-parser@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: cd391b7993a8e2397e3f0abd25e9b0b43747fe1b1415e9c5af6a3895567bb8dc9267d97e7d494c504f721b0b868bf9e0b93ec3677a5a7bc4ef2ff7252a402985
  languageName: node
  linkType: hard

"@aws-sdk/service-error-classification@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/service-error-classification@npm:3.257.0"
  checksum: 231440f52a1c6c6d5ab62c13491736273fda49bb68bc278954dfaa3b7fa874946576c87a529504d6268472e646b3235cacb5c0fe5acc9af2398d87a56fabda3d
  languageName: node
  linkType: hard

"@aws-sdk/shared-ini-file-loader@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/shared-ini-file-loader@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 27738db81e2b9c8e0bd248e1c0ed7f21b65b52204b247cb8119b3b48b55844c2450e5fe20bb1193311177a8a18e8bb99594d3bfde71dacd1ee9ed8bbad9a915b
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/signature-v4@npm:3.257.0"
  dependencies:
    "@aws-sdk/is-array-buffer": 3.201.0
    "@aws-sdk/types": 3.257.0
    "@aws-sdk/util-hex-encoding": 3.201.0
    "@aws-sdk/util-middleware": 3.257.0
    "@aws-sdk/util-uri-escape": 3.201.0
    "@aws-sdk/util-utf8": 3.254.0
    tslib: ^2.3.1
  checksum: 6faf6a3d8f51603240f6375b76bfd031223aa5cf1f0385cfa97833e6b188fb9b746caab53a45d0e7a311ca9735c82b56c67aed68b1fbcdf4bbcbf93e288fd09f
  languageName: node
  linkType: hard

"@aws-sdk/smithy-client@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/smithy-client@npm:3.257.0"
  dependencies:
    "@aws-sdk/middleware-stack": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 7e0acde87b143b2f4b20d2ddf54cd641c8fc9ed2a5f07014b3e2c3e8dafdabe94652665b6173aada4330eba9b6de9b2506989a15ada225c24dd24bc7d6eacd30
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/token-providers@npm:3.259.0"
  dependencies:
    "@aws-sdk/client-sso-oidc": 3.259.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/shared-ini-file-loader": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 77801f98a80d3d2d0c47d948eb1c50f3547aa377bfc4dc4a5a8457ce90ed7c0948ab7c3a11d8fb57646b8b5542377b50400e6c7146bba90168fee132ad6de3e0
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.257.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.257.0
  resolution: "@aws-sdk/types@npm:3.257.0"
  dependencies:
    tslib: ^2.3.1
  checksum: eab2154f7d27d0bec29df41374481652525e1658ad987c64ed02cbe7b7388c563e6a8d317c8fc564ac6fbd97c98be700a29352c9acebc3903cf3305431bf0a86
  languageName: node
  linkType: hard

"@aws-sdk/url-parser@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/url-parser@npm:3.257.0"
  dependencies:
    "@aws-sdk/querystring-parser": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: 3b781c7fe94572e673e0651a47806cf023f60fb1268d2abbad9364c70112b1ad37d2261f631e0d7ce5c5ec543faa7e316d987e519091db68bf121384ad557043
  languageName: node
  linkType: hard

"@aws-sdk/util-base64@npm:3.208.0":
  version: 3.208.0
  resolution: "@aws-sdk/util-base64@npm:3.208.0"
  dependencies:
    "@aws-sdk/util-buffer-from": 3.208.0
    tslib: ^2.3.1
  checksum: 2ccab3453a3a3636f3f1397441574b3adb984e1ba3865030393108327ed7304cf80c9b31d69691e6aba85cfe6a611a881bbb724e544324240763bb4e96630ed9
  languageName: node
  linkType: hard

"@aws-sdk/util-body-length-browser@npm:3.188.0":
  version: 3.188.0
  resolution: "@aws-sdk/util-body-length-browser@npm:3.188.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 1b08bd1e63ec843ee336f51d894c49bf3c4c2f96e50d1711a12f7d0c5b6f7a15b490e366fec55b63e77036002994bac12927b29de2eb9ac91e4f152b1af78e58
  languageName: node
  linkType: hard

"@aws-sdk/util-body-length-node@npm:3.208.0":
  version: 3.208.0
  resolution: "@aws-sdk/util-body-length-node@npm:3.208.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 986b42b358656dec4e75c231213331c4f01785f9ab17c8b87b6e268b6880818a96117f1785cef9786e6c0f7e2c1332c80e8388a43bfd83e8c7224ad059a72733
  languageName: node
  linkType: hard

"@aws-sdk/util-buffer-from@npm:3.208.0":
  version: 3.208.0
  resolution: "@aws-sdk/util-buffer-from@npm:3.208.0"
  dependencies:
    "@aws-sdk/is-array-buffer": 3.201.0
    tslib: ^2.3.1
  checksum: 00bfa4d4494d3a1eb128e19104994d1aca8b3802e9aa218cecafb1ed3ff2ecf5c946485e06aa97ae312458842b0f31a6484dc945232f7cb0e357ba341cb2e53e
  languageName: node
  linkType: hard

"@aws-sdk/util-config-provider@npm:3.208.0":
  version: 3.208.0
  resolution: "@aws-sdk/util-config-provider@npm:3.208.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 97b0414b120b4eb53001f3ab2135ee94937e47bd7bd0d0de7c6a7e00a282eaa78cd84be2bfd3e389340f0c0b2f7ba60da9a403f084721970ee55b779ecf7a451
  languageName: node
  linkType: hard

"@aws-sdk/util-defaults-mode-browser@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/util-defaults-mode-browser@npm:3.257.0"
  dependencies:
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/types": 3.257.0
    bowser: ^2.11.0
    tslib: ^2.3.1
  checksum: 85d30e2f0875f454f8e5b8668908a3d4939959077f074832099454157467a59d5d23219cff48deb2699966782475c153e10cd16ec7279ffe27f031b9c8f4fb75
  languageName: node
  linkType: hard

"@aws-sdk/util-defaults-mode-node@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/util-defaults-mode-node@npm:3.259.0"
  dependencies:
    "@aws-sdk/config-resolver": 3.259.0
    "@aws-sdk/credential-provider-imds": 3.259.0
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/property-provider": 3.257.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: c3cc5267377135c8f0b3529837e5d9b1a9b4633bb3c99749d334bb7bcf9ecde01fb0325199ce55e35a389353b1757bbb513137714d53888172f2b7babf3014f5
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/util-endpoints@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  checksum: ba1aec13c8ceb9519400f2c77db1743b34dec4380b27598a8862e94c9df43e3e8b4e183f230fb6bdaaeb89653d232e6a13dcc2016c6a9d12246f58b630fd3ec0
  languageName: node
  linkType: hard

"@aws-sdk/util-hex-encoding@npm:3.201.0":
  version: 3.201.0
  resolution: "@aws-sdk/util-hex-encoding@npm:3.201.0"
  dependencies:
    tslib: ^2.3.1
  checksum: a27f3365dfb1e6ece79ea34fd6e2c4540eb0084536d7300ff0ff42a7334ddf07f21958c6cfd0bbeb71361ee408e16deae2c82b7c7378b048b8e81a52c75f190a
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.208.0
  resolution: "@aws-sdk/util-locate-window@npm:3.208.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 7518c110c4fa27c5e1d2d173647f1c58fc6ea244d25733c08ac441d3a2650b050ce06cecbe56b80a9997d514c9f7515b3c529c84c1e04b29aa0265d53af23c52
  languageName: node
  linkType: hard

"@aws-sdk/util-middleware@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/util-middleware@npm:3.257.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 77ac007288fa98ec55c10af5a097f7f829d7ec53b6eecc43bc00a18b9ea011194aec42fb69e58421f87363490cef89b811330eaf174739fdf6b810b40cd46768
  languageName: node
  linkType: hard

"@aws-sdk/util-retry@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/util-retry@npm:3.257.0"
  dependencies:
    "@aws-sdk/service-error-classification": 3.257.0
    tslib: ^2.3.1
  checksum: 7d7054c5a73f3acaf59b620386ad0a6226ec6b2761e5c59f304694f8a7f5cb8ab0eb7d51be102e81d6e2a5ec97b20bd8814cf245ff44fcc5587d4e5b80a3b892
  languageName: node
  linkType: hard

"@aws-sdk/util-uri-escape@npm:3.201.0":
  version: 3.201.0
  resolution: "@aws-sdk/util-uri-escape@npm:3.201.0"
  dependencies:
    tslib: ^2.3.1
  checksum: 8bd751459eaab75a9b61801f3484cfa5c4e0133381ace6ec901cb9b92b1fee99beb4ef9c0f87ade59425a882ed3a280255d9b2fd8da6a6286e49efb9af8f0d55
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.257.0":
  version: 3.257.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.257.0"
  dependencies:
    "@aws-sdk/types": 3.257.0
    bowser: ^2.11.0
    tslib: ^2.3.1
  checksum: f475d6096ff92ba2292de6cc78e2e1512df96c7fff3d8ef39148884533c14cc341a84ce3c4969f78cca2d8d4098c4cefefd72edf7cb480c33f8597f5828245c3
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.259.0":
  version: 3.259.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.259.0"
  dependencies:
    "@aws-sdk/node-config-provider": 3.259.0
    "@aws-sdk/types": 3.257.0
    tslib: ^2.3.1
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: ed2ae75ef0ccd5f58f16887c8cfd521006e8efaa8431420d1b55c3f885c501bbb06e6a4f262c0239a88fbec9100923cd277b4539838df9fe9f391c411c5767ee
  languageName: node
  linkType: hard

"@aws-sdk/util-utf8-browser@npm:^3.0.0":
  version: 3.259.0
  resolution: "@aws-sdk/util-utf8-browser@npm:3.259.0"
  dependencies:
    tslib: ^2.3.1
  checksum: b6a1e580da1c9b62c749814182a7649a748ca4253edb4063aa521df97d25b76eae3359eb1680b86f71aac668e05cc05c514379bca39ebf4ba998ae4348412da8
  languageName: node
  linkType: hard

"@aws-sdk/util-utf8@npm:3.254.0":
  version: 3.254.0
  resolution: "@aws-sdk/util-utf8@npm:3.254.0"
  dependencies:
    "@aws-sdk/util-buffer-from": 3.208.0
    tslib: ^2.3.1
  checksum: e5dfe7565f2de32245a544d1d715d803025bc5522538c0206fa61377f747804d95fc2e5e25976144bb63a6857e360b4286d101e730ab5d39866c60383a47e7d5
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": 0.3.9
  checksum: 5718f267085ed8edb3e7ef210137241775e607ee18b77d95aa5bd7514f47f5019aa2d82d96b3bf342ef7aa890a346fa1044532ff7cc3009e7d24fce3ce6200fa
  languageName: node
  linkType: hard

"@graphql-typed-document-node/core@npm:^3.1.1":
  version: 3.1.1
  resolution: "@graphql-typed-document-node/core@npm:3.1.1"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 87ff4cee308f1075f4472b80f9f9409667979940f8f701e87f0aa35ce5cf104d94b41258ea8192d05a0893475cd0f086a3929a07663b4fe8d0e805a277f07ed5
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3":
  version: 3.1.0
  resolution: "@jridgewell/resolve-uri@npm:3.1.0"
  checksum: b5ceaaf9a110fcb2780d1d8f8d4a0bfd216702f31c988d8042e5f8fbe353c55d9b0f55a1733afdc64806f8e79c485d2464680ac48a0d9fcadb9548ee6b81d267
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.4.14
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.14"
  checksum: 61100637b6d173d3ba786a5dff019e1a74b1f394f323c1fee337ff390239f053b87266c7a948777f4b1ee68c01a8ad0ab61e5ff4abb5a012a0b091bec391ab97
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": ^3.0.3
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: d89597752fd88d3f3480845691a05a44bd21faac18e2185b6f436c3b0fd0c5a859fbbd9aaa92050c4052caf325ad3e10e2e1d1b64327517471b7d51babc0ddef
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.9
  resolution: "@tsconfig/node10@npm:1.0.9"
  checksum: a33ae4dc2a621c0678ac8ac4bceb8e512ae75dac65417a2ad9b022d9b5411e863c4c198b6ba9ef659e14b9fb609bbec680841a2e84c1172df7a5ffcf076539df
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 5ce29a41b13e7897a58b8e2df11269c5395999e588b9a467386f99d1d26f6c77d1af2719e407621412520ea30517d718d5192a32403b8dfcc163bf33e40a338a
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 19275fe80c4c8d0ad0abed6a96dbf00642e88b220b090418609c4376e1cef81bf16237bf170ad1b341452feddb8115d8dd2e5acdfdea1b27422071163dc9ba9d
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.3
  resolution: "@tsconfig/node16@npm:1.0.3"
  checksum: 3a8b657dd047495b7ad23437d6afd20297ce90380ff0bdee93fc7d39a900dbd8d9e26e53ff6b465e7967ce2adf0b218782590ce9013285121e6a5928fbd6819f
  languageName: node
  linkType: hard

"@types/aws-lambda@npm:^8.10.103":
  version: 8.10.103
  resolution: "@types/aws-lambda@npm:8.10.103"
  checksum: fcca2db92ff7fea4291e2f015d7f5a5de259adac68df3db2b622c92dd48212fd7f60af832c70917448dcc076619cb37bca8636b7bfaf58cd53fc82394a5cbe3d
  languageName: node
  linkType: hard

"@types/node@npm:^20.3.3":
  version: 20.3.3
  resolution: "@types/node@npm:20.3.3"
  checksum: 7a0d00800451ca8cd8df63a8cc218c697edadb3143bf46cd6afeb974542a6a1665c3679459be0016c29216ccfed6616b7e55851747527dfa71c5608d9157528c
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^8.4.1":
  version: 8.8.0
  resolution: "acorn@npm:8.8.0"
  bin:
    acorn: bin/acorn
  checksum: 7270ca82b242eafe5687a11fea6e088c960af712683756abf0791b68855ea9cace3057bd5e998ffcef50c944810c1e0ca1da526d02b32110e13c722aa959afdc
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 544af8dd3f60546d3e4aff084d451b96961d2267d668670199692f8d054f0415d86fc5497d0e641e91546f0aa920e7c29e5250e99fc89f5552a34b5d93b77f43
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 29c3f01f22e703fa6644fc3b684307442df4240b6e10f6cfe1b61c6ca5721073189ca97cdeedb376081148c8518e33b1d818a57f781d70b0b70e1f31fb48814f
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: a9a1503d4390d8b59ad86f4607de7870b39cad43d929813599a23714831e81c520bddf61bcdd1f8e30f05fd3a2b71ae8538e946eb2786dc65c2bbc520f692eff
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.1.5
  resolution: "cross-fetch@npm:3.1.5"
  dependencies:
    node-fetch: 2.6.7
  checksum: f6b8c6ee3ef993ace6277fd789c71b6acf1b504fd5f5c7128df4ef2f125a429e29cd62dc8c127523f04a5f2fa4771ed80e3f3d9695617f441425045f505cf3bb
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: f2c09b0ce4e6b301c221addd83bf3f454c0bc00caa3dd837cf6c127d6edf7223aa2bbe3b688feea110b7f262adbfc845b757c44c8a9f8c0c5b15d8fa9ce9d20d
  languageName: node
  linkType: hard

"dotenv@npm:^16.0.2":
  version: 16.0.2
  resolution: "dotenv@npm:16.0.2"
  checksum: ca8f9ca2d67929c7771069f4c31b4e46b9932621009e658e5afd655dde2d69b77642bf36dbc9e72bc170523dfd908a9ee41c26f034c1fdc605ace3b1b4b10faf
  languageName: node
  linkType: hard

"extract-files@npm:^9.0.0":
  version: 9.0.0
  resolution: "extract-files@npm:9.0.0"
  checksum: c31781d090f8d8f62cc541f1023b39ea863f24bd6fb3d4011922d71cbded70cef8191f2b70b43ec6cb5c5907cdad1dc5e9f29f78228936c10adc239091d8ab64
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.0.11":
  version: 4.0.11
  resolution: "fast-xml-parser@npm:4.0.11"
  dependencies:
    strnum: ^1.0.5
  bin:
    fxparser: src/cli/cli.js
  checksum: d8a08e4d5597e0fc00a86735195872eeb03008913e298830941516f3766e16ee555e2d431acc92e1dda887938edc445252ec5b59494aab60a8389888bd13719c
  languageName: node
  linkType: hard

"form-data@npm:^3.0.0":
  version: 3.0.1
  resolution: "form-data@npm:3.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: b019e8d35c8afc14a2bd8a7a92fa4f525a4726b6d5a9740e8d2623c30e308fbb58dc8469f90415a856698933c8479b01646a9dff33c87cc4e76d72aedbbf860d
  languageName: node
  linkType: hard

"graphql-request@npm:^5.2.0":
  version: 5.2.0
  resolution: "graphql-request@npm:5.2.0"
  dependencies:
    "@graphql-typed-document-node/core": ^3.1.1
    cross-fetch: ^3.1.5
    extract-files: ^9.0.0
    form-data: ^3.0.0
  peerDependencies:
    graphql: 14 - 16
  checksum: a8aa37816378898e6fc8c4db04a1c114c98f98d90718cf1680bd96b22724bd43b1210619f9b0d328b5c1acb4f7b76d2227a2537cd5ab059bb54cf0debecb33bf
  languageName: node
  linkType: hard

"graphql@npm:^16.6.0":
  version: 16.6.0
  resolution: "graphql@npm:16.6.0"
  checksum: bf1d9e3c1938ce3c1a81e909bd3ead1ae4707c577f91cff1ca2eca474bfbc7873d5d7b942e1e9777ff5a8304421dba57a4b76d7a29eb19de8711cb70e3c2415e
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: b86e5e0e25f7f777b77fabd8e2cbf15737972869d852a22b7e73c17623928fccb826d8e46b9951501d3f20e51ad74ba8c59ed584f610526a48f8ccf88aaec402
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"node-fetch@npm:2.6.7":
  version: 2.6.7
  resolution: "node-fetch@npm:2.6.7"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 8d816ffd1ee22cab8301c7756ef04f3437f18dace86a1dae22cf81db8ef29c0bf6655f3215cb0cdb22b420b6fe141e64b26905e7f33f9377a7fa59135ea3e10b
  languageName: node
  linkType: hard

"post-confirmation-lambda@workspace:.":
  version: 0.0.0-use.local
  resolution: "post-confirmation-lambda@workspace:."
  dependencies:
    "@aws-sdk/client-cognito-identity-provider": ^3.259.0
    "@types/aws-lambda": ^8.10.103
    "@types/node": ^20.3.3
    dotenv: ^16.0.2
    graphql: ^16.6.0
    graphql-request: ^5.2.0
    ts-node: ^10.9.1
    typescript: ^4.8.3
  languageName: unknown
  linkType: soft

"strnum@npm:^1.0.5":
  version: 1.0.5
  resolution: "strnum@npm:1.0.5"
  checksum: 651b2031db5da1bf4a77fdd2f116a8ac8055157c5420f5569f64879133825915ad461513e7202a16d7fec63c54fd822410d0962f8ca12385c4334891b9ae6dd2
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.1":
  version: 10.9.1
  resolution: "ts-node@npm:10.9.1"
  dependencies:
    "@cspotcode/source-map-support": ^0.8.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.1
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 090adff1302ab20bd3486e6b4799e90f97726ed39e02b39e566f8ab674fd5bd5f727f43615debbfc580d33c6d9d1c6b1b3ce7d8e3cca3e20530a145ffa232c35
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.3.1":
  version: 2.5.0
  resolution: "tslib@npm:2.5.0"
  checksum: ae3ed5f9ce29932d049908ebfdf21b3a003a85653a9a140d614da6b767a93ef94f460e52c3d787f0e4f383546981713f165037dc2274df212ea9f8a4541004e1
  languageName: node
  linkType: hard

"typescript@npm:^4.8.3":
  version: 4.8.3
  resolution: "typescript@npm:4.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 8286a5edcaf3d68e65c451aa1e7150ad1cf53ee0813c07ec35b7abdfdb10f355ecaa13c6a226a694ae7a67785fd7eeebf89f845da0b4f7e4a35561ddc459aba0
  languageName: node
  linkType: hard

"typescript@patch:typescript@^4.8.3#~builtin<compat/typescript>":
  version: 4.8.3
  resolution: "typescript@patch:typescript@npm%3A4.8.3#~builtin<compat/typescript>::version=4.8.3&hash=f456af"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 0404a09c625df01934ef774b45ce1ca57ccae41cd625fcdbb82056715320d9329e70d9d75c2c732ec6ef947444ca978c189a332b71fa21f5c1437d5a83e24906
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 78089ad549e21bcdbfca10c08850022b22024cdcc2da9b168bcf5a73a6ed7bf01a9cebb9eac28e03cd23a684d81e0502797e88f3ccd27a32aeab1cfc44c39da0
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 2c487b0e149e746ef48cda9f8bad10fc83693cd69d7f9dcd8be4214e985de33a29c9e24f3c0d6bcf2288427040a8947406ab27f7af67ee9456e6b84854f02dd6
  languageName: node
  linkType: hard
