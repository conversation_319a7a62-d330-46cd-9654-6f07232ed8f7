{"name": "easygarage-backend", "version": "1.0.0", "main": "index.js", "repository": "**************:TudorBNG/easygarage-backend.git", "author": "Cosmin & Tudor", "license": "MIT", "private": true, "workspaces": ["packages/user-server", "packages/appointments-server", "packages/gateway-server"], "scripts": {"install:all": "yarn workspaces foreach -p install", "start:podman:servers": "podman compose up --build --detach", "start:podman:services": "cd services && podman compose up --build --detach", "start:podman": "yarn start:podman:services & yarn start:podman:servers", "dynamodb:init": "curl -X DELETE http://localhost:8001/tables && AWS_PAGER=\"\" find ./dynamodb/tables -maxdepth 1 -type f -exec aws dynamodb create-table --endpoint-url http://localhost:8000 --cli-input-json file://\\{\\} \\; ", "dynamodb:seed": "aws dynamodb batch-write-item --request-items file://dynamodb/seed/dynamo-seed.json --endpoint-url http://localhost:8000", "dynamodb:remove": "podman container rm dynamodb-local", "dynamodb:admin": "DYNAMO_ENDPOINT=http://localhost:8000  AWS_REGION=eu-central-1 AWS_ACCESS_KEY_ID=xxxx AWS_SECRET_ACCESS_KEY=xxxx dynamodb-admin", "dynamodb:refresh": "yarn dynamodb:init && yarn dynamodb:seed", "lint": "eslint . --ignore-path .gitignore --ext .ts,.tsx,.js", "lint:fix": "yarn lint --fix"}, "devDependencies": {"@react-native-community/eslint-config": "^3.2.0", "eslint": "^8.22.0", "prettier": "^2.7.1", "typescript": "^4.7.4"}}