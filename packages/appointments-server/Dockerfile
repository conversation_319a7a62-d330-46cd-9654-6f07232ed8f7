FROM node:18.17.1 as base

EXPOSE 4002

FROM base as dev

RUN mkdir -p /usr/src/app

WORKDIR /usr/src/app

COPY package.json /usr/src/app/

RUN yarn install

COPY --chown=node . /usr/src/app

CMD ["yarn","start:dev"]

## Production build stage
FROM base as build

WORKDIR /usr/src/build

COPY . .

RUN yarn install

RUN yarn build


## Prepare production environment
FROM base as prod

WORKDIR /usr/src/app

ENV NODE_ENV production

COPY --chown=node package.json .

RUN yarn install

RUN chown -R node node_modules

COPY --chown=node --from=build /usr/src/build/dist dist

CMD [ "yarn", "start" ]