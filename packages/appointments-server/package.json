{"name": "appointments-server", "version": "1.0.0", "main": "src/index.ts", "license": "MIT", "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json && copyfiles -u 3 src/graphql/schema/**/*.graphql dist/graphql/schema", "clean": "rm -rf dist/", "start:dev": "nodemon src/index.ts", "start": "node ./dist/index.js", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@apollo/subgraph": "^2.0.5", "@types/stripe": "^8.0.417", "apollo-server-core": "^3.10.1", "apollo-server-express": "^3.10.0", "axios": "^1.3.2", "copyfiles": "^2.4.1", "dotenv": "^16.0.1", "express": "^4.18.1", "graphql": "^16.5.0", "graphql-request": "^5.2.0", "json-bigint": "^1.0.0", "pg": "^8.8.0", "schemaglue": "^4.1.0", "stripe": "^17.4.0", "ts-node": "^10.9.1"}, "devDependencies": {"@types/express": "^4.17.13", "@types/node": "^18.6.5", "@types/pg": "8.10.3", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "husky": "^8.0.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "typescript": "^4.7.4"}}