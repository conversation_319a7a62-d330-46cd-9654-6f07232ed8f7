import { ApolloServer, gql } from 'apollo-server-express';

import { buildSubgraphSchema } from '@apollo/subgraph';

import path from 'path';

import glue from 'schemaglue';

import env from './env';

// Apollo server landing pages
import {
	ApolloServerPluginLandingPageDisabled,
	ApolloServerPluginLandingPageGraphQLPlayground,
	ApolloServerPluginDrainHttpServer,
} from 'apollo-server-core';

// Set the Apollo server landing page
const landingPage =
	env.NODE_ENV === 'production'
		? ApolloServerPluginLandingPageDisabled()
		: ApolloServerPluginLandingPageGraphQLPlayground();

// Glue schemas/resolvers together
const baseDir = path.resolve(__dirname, '');

const schemaDir = path.join(baseDir, './graphql/schema');

const resolverDir = path.relative(schemaDir, __dirname);

const { schema, resolver } = glue(schemaDir, {
	mode: `${resolverDir}/**/*.[jt]s`,
	ignore: `${resolverDir}/**/*.test.[jt]s`,
});

// Initialise Apollo server
const initialiseApolloServer = (app: any) => {
	const server = new ApolloServer({
		cache: 'bounded',
		schema: buildSubgraphSchema({
			typeDefs: gql(schema),
			resolvers: resolver,
		}),
		plugins: [
			// tracer, -to be reviewed
			landingPage,
			ApolloServerPluginDrainHttpServer({ httpServer: app }),
		],
		// logger, -to be reviewed
		context: async ({ connection, req }: any) => {
			// Filter out subscriptions
			if (connection) return connection.context;
			// Extract user Id from federation servers forwarded headers
			// const userId = 'APOLLO-INTERNAL';
			const userId = req.headers['x-apollo-user-id'] || null;
			// Extract federation server log trace id
			const logTraceId = req.headers['x-log-trace-id'] || 'unspecified';
			// Apply context to request
			return {
				userId,
				logTraceId,
				jaegersession: req.headers.jaegersession,
				jaegerrequest: req.headers.jaegerrequest,
			};
		},
	});
	// Middleware: GraphQL
	server.start().then(() =>
		server.applyMiddleware({
			app,
		}),
	);
};

export default initialiseApolloServer;
