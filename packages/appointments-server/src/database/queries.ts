export const GET_SERVICE_TYPES = 'SELECT * FROM customereg."ServiceTypes"';
export const GET_CAR_MAKES = 'SELECT * FROM customereg."CarMakes"';
export const ADD_USER_SERVICE = (queryFields, valueIds) =>
	`INSERT INTO customereg."Services" (${queryFields}) VALUES (${valueIds});`;

export const GET_USER_SERVICES =
	'SELECT * FROM customereg."Services" s INNER JOIN customereg."ServiceTypes" st ON s.service_type_id=st.service_type_id WHERE user_id=$1;';
