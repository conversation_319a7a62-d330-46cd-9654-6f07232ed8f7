import { randomUUID } from 'crypto';
import dbClient from '../../../database/db.config';
import { createValueIds } from '../../../database/db.helpers';

import {
	ADD_USER_SERVICE,
	GET_SERVICE_TYPES,
	GET_USER_SERVICES,
} from '../../../database/queries';

import {
	mapDBUserServiceFields,
	mapServiceTypesResponse,
	mapServicesResponse,
} from '../../../utils/mappers';

export const getServiceTypes = async () => {
	try {
		const result = await dbClient.query(GET_SERVICE_TYPES);

		const serviceTypes =
			result.rows.length && mapServiceTypesResponse(result.rows);
		return serviceTypes;
	} catch (err) {
		console.log('Error getting user from DB', err.message);
		throw err;
	}
};

export const storeServices = async (services, userId, appointmentsCapacity) => {
	try {
		for (const service of services) {
			service.serviceId = randomUUID();
			service.userId = userId;
			service.appointments = appointmentsCapacity;

			const queryFields = mapDBUserServiceFields(service);

			const values = [
				...Object.values(service).filter(
					(serviceField) =>
						serviceField !== null && serviceField !== undefined,
				),
			];

			const valueIds = createValueIds(values);

			await dbClient.query(
				ADD_USER_SERVICE(queryFields, valueIds),
				values,
			);
		}

		return { status: 201, message: 'User added successfully' };
	} catch (err) {
		console.log('Error putting user in DB', err.message);
		throw err;
	}
};

export const getUserServices = async (userId) => {
	try {
		const result = await dbClient.query(GET_USER_SERVICES, [userId]);

		const services = result.rows.length
			? mapServicesResponse(result.rows)
			: [];
		return services;
	} catch (err) {
		console.log('Error getting user from DB', err.message);
		throw err;
	}
};
