import { userAuthorised, isInternal } from '../../../utils/auth';

import {
	getServiceTypes,
	getUserServices,
	storeServices,
} from './appointments.functions';

const appointmentsResolver = {
	Query: {
		getServiceTypes: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				console.log('Fetching service types...');
				try {
					const response = await getServiceTypes();

					return {
						status: 200,
						message: 'Service types retrieved successfully',
						serviceTypes: response,
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
		getUserServices: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				console.log('Fetching user services...');
				try {
					const response = await getUserServices(args.userId);

					return {
						status: 200,
						message: 'Services retrieved successfully',
						services: response,
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},

	Mutation: {
		storeServices: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				console.log('Adding user services...');
				try {
					await storeServices(
						args.services,
						args.userId,
						args.appointments,
					);

					return {
						status: 200,
						message: 'Services added successfully',
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},
};

exports.resolver = appointmentsResolver;
