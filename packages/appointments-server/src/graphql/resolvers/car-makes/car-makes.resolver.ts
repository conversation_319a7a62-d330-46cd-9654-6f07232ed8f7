import { userAuthorised, isInternal } from '../../../utils/auth';

import { getCarMakes } from './car-makes.functions';

const carMakesResolver = {
	Query: {
		getCarMakes: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				console.log('Fetching car makes...');
				try {
					const response = await getCarMakes();

					return {
						status: 200,
						message: 'Car makes retrieved successfully',
						carMakes: response,
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},

	Mutation: {},
};

exports.resolver = carMakesResolver;
