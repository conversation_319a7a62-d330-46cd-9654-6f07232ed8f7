import express from 'express';
import initialiseApolloServer from './apolloServer';
import client from './database/db.config';

const app = express();

const PORT = 4002;

client.connect((error) => {
	if (error) throw error;
	console.log('Connected to database');
});

// Initialise Apollo server
initialiseApolloServer(app);

app.get('/', (request, response) => {
	response.send('Hello!');
});

app.listen(PORT, () => {
	console.log('Appointments server started on port ', PORT);
});
