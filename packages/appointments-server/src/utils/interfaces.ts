export interface ServiceType {
	serviceTypeId: String;
	name: String;
}

export interface ServiceTypeDB {
	service_type_id: String;
	name: String;
}

export interface ServiceDB {
	service_type_id: string;
	start_hour: string;
	end_hour: string;
	allowed_car_makes: string;
	parallel_appointment_capacity: string;
	price: string;
	service_id: string;
	user_id: string;
	//coming from join query
	name?: string;
}

export interface Service {
	serviceTypeId: string;
	serviceName: string;
	startTime: string;
	endTime: string;
	allowedCarMakes: string;
	appointments: string;
	price: string;
	serviceId: string;
	userId: string;
}
