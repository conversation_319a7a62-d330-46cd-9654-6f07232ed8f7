import { Service, ServiceDB, ServiceType, ServiceTypeDB } from './interfaces';

export const mapServiceTypesResponse = (
	serviceTypes: ServiceTypeDB[],
): ServiceType[] =>
	serviceTypes.map((serviceType) => ({
		serviceTypeId: serviceType.service_type_id,
		name: serviceType.name,
	}));

export const mapDBUserServiceFields = (service) => {
	const serviceKeys = {
		serviceTypeId: 'service_type_id',
		startTime: 'start_hour',
		endTime: 'end_hour',
		allowedCarMakes: 'allowed_car_makes',
		price: 'price',
		serviceId: 'service_id',
		userId: 'user_id',
		appointments: 'parallel_appointment_capacity',
	};

	const serviceFields = [];
	Object.keys(serviceKeys).forEach(
		(key) =>
			service[key] !== null &&
			service[key] !== undefined &&
			serviceFields.push(serviceKeys[key]),
	);
	return serviceFields.join(', ');
};

export const mapServicesResponse = (services: ServiceDB[]): Service[] =>
	services.map((service) => ({
		serviceTypeId: service.service_type_id,
		serviceName: service.name,
		startTime: service.start_hour,
		endTime: service.end_hour,
		allowedCarMakes: service.allowed_car_makes,
		appointments: service.parallel_appointment_capacity,
		price: service.price,
		serviceId: service.service_id,
		userId: service.user_id,
	}));
