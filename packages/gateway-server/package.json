{"name": "gateway-server", "version": "1.0.0", "main": "src/index.ts", "license": "MIT", "scripts": {"start": "node ./dist/index.js", "start:dev": "nodemon src/index.ts", "prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf dist/", "docker:start": "docker build -t whispr-backend . && docker run -p 8080:8080 whispr-backend", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@types/express": "^4.17.13", "@types/node": "^18.6.5", "@types/node-fetch": "^2.6.2", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "husky": "^8.0.1", "node-fetch": "^2.6.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "typescript": "^4.7.4"}, "dependencies": {"@apollo/gateway": "^2.0.3", "apollo-server": "^3.10.0", "apollo-server-core": "^3.10.0", "aws-sdk": "^2.1209.0", "axios": "^0.27.2", "dotenv": "^16.0.1", "express": "^4.18.1", "graphql": "^16.5.0", "jsonwebtoken": "^8.5.1", "jwk-to-pem": "^2.0.5", "ts-node": "^10.9.1", "util": "^0.12.4"}}