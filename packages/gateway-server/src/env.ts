import dotenv from 'dotenv';
dotenv.config();

const requiredEnvVars = [
	'NODE_ENV',
	'INTERNAL_USER_HTTP',
	'INTERNAL_APPOINTMENTS_HTTP',
	'APOLLO_INTERNAL_SECRET',
	'APOLL<PERSON>_INTERNAL_USER_ID',
	'IDP_BASE_ISSUER',
	'AWS_DEFAULT_REGION',
	'COGNITO_USER_POOL_ID',
];

const missingVars = requiredEnvVars.filter(
	(variable) => process.env[variable] === undefined,
);

if (missingVars.length > 0) {
	console.log('Missing env variables: ', missingVars);
}

const {
	NODE_ENV,
	INTERNAL_USER_HTTP,
	INTERNAL_APPOINTMENTS_HTTP,
	APOLLO_INTERNAL_SECRET,
	APOLLO_INTERNAL_USER_ID,
	IDP_BASE_ISSUER,
	AWS_DEFAULT_REGION,
	COGNITO_USER_POOL_ID,
} = process.env;

export default {
	NODE_ENV,
	INTERNAL_USER_HTTP,
	INTERNAL_APPOINTMENTS_HTTP,
	APOLLO_INTERNAL_SECRET,
	APOLLO_INTERNAL_USER_ID,
	IDP_BASE_ISSUER,
	AWS_DEFAULT_REGION,
	COGNITO_USER_POOL_ID,
};
