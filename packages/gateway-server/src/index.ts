import env from './env';
import { extractUser } from './utils/auth';
import {
	ApolloServerPluginLandingPageDisabled,
	ApolloServerPluginLandingPageLocalDefault,
} from 'apollo-server-core';
import {
	ApolloGateway,
	IntrospectAndCompose,
	RemoteGraphQLDataSource,
	ServiceEndpointDefinition,
} from '@apollo/gateway';
import { ApolloServer } from 'apollo-server';
import subgraphs from './config';
import { ValueOrPromise } from 'apollo-server-types';

const landingPage =
	env.NODE_ENV === 'production'
		? ApolloServerPluginLandingPageDisabled()
		: ApolloServerPluginLandingPageLocalDefault();

class AuthenticatedDataSource extends RemoteGraphQLDataSource {
	willSendRequest({ request, context }): ValueOrPromise<void> {
		const { headers, userId, logTraceId, issuer } = context;

		if (request?.http?.headers) {
			// Forward the authenticated user Id to federated services
			request.http.headers.set('x-apollo-user-id', userId);
			request.http.headers.set('x-apollo-issuer', issuer);
			// Generate a federation server trace id for tracking in federated services
			request.http.headers.set('x-log-trace-id', logTraceId);
		}
		if (headers) {
			Object.keys(headers).forEach(
				(key) =>
					request.http && request.http.headers.set(key, headers[key]),
			);
		}
	}
}

const gateway = new ApolloGateway({
	supergraphSdl: new IntrospectAndCompose({
		subgraphs,
		pollIntervalInMs: 3000,
	}),
	buildService({ url }: ServiceEndpointDefinition) {
		return new AuthenticatedDataSource({ url });
	},
});

const server = new ApolloServer({
	cache: 'bounded',
	gateway,
	introspection: env.NODE_ENV !== 'production',
	context: async ({ req }) => {
		const user = await extractUser(
			req.headers.authorization,
			req.headers['x-apollo-internal-secret'],
		);

		// Apply context to request
		return {
			headers: req.headers,
			jaegersession: req.headers.jaegersession,
			jaegerrequest: req.headers.jaegerrequest,
			issuer: user.issuer,
			userId: user.userId,
			client: user.client,
		};
	},
	plugins: [landingPage],
});

server.listen(4000).then(({ url }) => {
	console.log(`Gateway ready at url: ${url}`);
});
