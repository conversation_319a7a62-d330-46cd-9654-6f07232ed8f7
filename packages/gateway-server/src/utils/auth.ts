import { ApolloError, AuthenticationError } from 'apollo-server-core';
import env from '../env';
import { JwtPayload } from 'jsonwebtoken';
import { TokenIssuerMap } from './interfaces';
import { decodeToken } from './authHelper';

/**
 * Extracts the users IDP user id from the tokens sub claim
 * sub represents a unique identifier (UUID) for the authenticated user.
 * @param token decoded jwt token
 */
const extractIdpUserId = (
	token: JwtPayload | null | undefined,
): string | null => {
	const sub = (token && token.sub) || '';
	return sub;
};

/**
 * Validates a users consents, i.e. Terms and Conditions, which must exist for a valid session
 * @param {TokenInterface} parsedToken id_token containing custom attributes for consents
 * @returns {ConsentStatus} contains status and validity
 */
const validateConsents = (
	parsedToken: JwtPayload | null | undefined,
): boolean => {
	const attributes = parsedToken?.custom_attributes;
	if (!attributes?.tc) {
		return false;
	}

	return attributes['tc-accepted'] === attributes['tc-latest-version'];
};

/**
 * Validates a JWT id_token against the IDP server that issued it to the client
 * @param token JWT id_token to be validate against IDP keystore
 */
const parseIdpToken = (token, mappedIssuer) => {
	const consent: boolean = mappedIssuer.validateConsent
		? validateConsents(token)
		: true;

	if (!consent) throw new ApolloError('', 'CONSENT');

	return {
		userId: extractIdpUserId(token),
		client: token?.aud || null,
		issuer: mappedIssuer.issuer || null,
	};
};

const issuers = [
	{
		issuer: env.IDP_BASE_ISSUER,
		validateConsent: false,
		parseFunction: parseIdpToken,
	},
];

// Reduced issuer mapping object, written to exclude issuers incase of undefined issuer property
const issuerMap: TokenIssuerMap = issuers.reduce((acc, curr) => {
	if (curr.issuer) {
		return { ...acc, [curr.issuer]: { ...curr } };
	}
	return acc;
}, {});

/**
 * Handles extracting the user context of a request to the GraphQL server
 * @param authorizationHeader authorization header in request containing JWT token
 * @param internalSecretHeader internal services secret header in request
 */
export const extractUser = async (
	authorizationHeader: string | undefined,
	internalSecretHeader: string | Array<string> | undefined,
) => {
	// If there is no authorizationHeader and there is a internalSecretHeader this indicates an internal request within our cloud infrastructure
	// i.e a Lambda using an IAM policy to make a request to one of our service via the gateway, passing the apollo internal secret
	// Internal calls to this service should not be passing an authorisation header, as there is no expectation of JWT validation internally
	// External traffic to this service is authenticated via JWT at our edge via an authoriser lambda, so we can safely perform this action
	if (!authorizationHeader && internalSecretHeader) {
		const apolloInternalSecret = env.APOLLO_INTERNAL_SECRET || 'secret';
		const apolloInternalUserId = env.APOLLO_INTERNAL_USER_ID || 'internal';
		if (internalSecretHeader === apolloInternalSecret) {
			return {
				userId: apolloInternalUserId,
				client: 'APOLLO-INTERNAL',
				issuer: 'N/A',
			};
		}
		throw new AuthenticationError('UNAUTHENTICATED');
	}
	const token = authorizationHeader.split(' ')[1];

	// Attempt to map the decoded JWT iss claim to an issuer in the mapped issuer object
	// The "iss" (issuer) claim identifies the principal that issued the
	// JWT.  The processing of this claim is generally application specific.
	const decoded = await decodeToken({ token });

	// Map the decoded tokens iss claim to the known issuer mapping object
	const mappedIssuer = issuerMap[decoded?.iss as string];

	// Although unlikely due to external requests having to be validated by the authoriser Lambda, we should still throw
	// an authorisation error here if a mapped issuer is not found for the decoded tokens iss claim, as this could indicate a configuration error
	// within our service security
	if (!decoded || !mappedIssuer?.issuer) {
		throw new AuthenticationError('UNAUTHENTICATED');
	}

	// Handle parsing the token for the token issuer
	return mappedIssuer.parseFunction(decoded, mappedIssuer);
};
