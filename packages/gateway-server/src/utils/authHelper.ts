import { promisify } from 'util';
import * as Axios from 'axios';
import * as jsonwebtoken from 'jsonwebtoken';
import env from '../env';
import jwkToPem from 'jwk-to-pem';

export interface ClaimVerifyRequest {
	readonly token?: string;
}

export interface ClaimVerifyResult {
	readonly iss: string;
	readonly sub: string;
	readonly aud: string;
	readonly userName: string;
	readonly clientId: string;
	readonly isValid: boolean;
	readonly error?: Error;
}

interface TokenHeader {
	kid: string;
	alg: string;
}
interface PublicKey {
	alg: string;
	e: string;
	kid: string;
	kty: string;
	n: string;
	use: string;
}
interface PublicKeyMeta {
	instance: PublicKey;
	pem: string;
}

interface PublicKeys {
	keys: PublicKey[];
}

interface MapOfKidToPublicKey {
	[key: string]: PublicKeyMeta;
}

interface Claim {
	sub: string;
	aud: string;
	token_use: string;
	auth_time: number;
	iss: string;
	exp: number;
	username: string;
	client_id: string;
}

const cognitoPoolId = env.COGNITO_USER_POOL_ID || '';
if (!cognitoPoolId) {
	throw new Error('env var required for cognito pool');
}
const cognitoIssuer = env.IDP_BASE_ISSUER || '';

let cacheKeys: MapOfKidToPublicKey | undefined;
const getPublicKeys = async (): Promise<MapOfKidToPublicKey> => {
	if (!cacheKeys) {
		const url = `${cognitoIssuer}/.well-known/jwks.json`;
		const publicKeys = await Axios.default.get<PublicKeys>(url);
		cacheKeys = publicKeys.data.keys.reduce((agg, current) => {
			const pem = jwkToPem(current);
			agg[current.kid] = { instance: current, pem };
			return agg;
		}, {} as MapOfKidToPublicKey);
		return cacheKeys;
	} else {
		return cacheKeys;
	}
};

const verifyPromised = promisify(jsonwebtoken.verify.bind(jsonwebtoken));

const decodeToken = async (
	request: ClaimVerifyRequest,
): Promise<ClaimVerifyResult> => {
	let result: ClaimVerifyResult;
	try {
		console.log(`user claim verify invoked for ${JSON.stringify(request)}`);
		const token = request.token;
		const tokenSections = (token || '').split('.');
		if (tokenSections.length < 2) {
			throw new Error('requested token is invalid');
		}
		const headerJSON = Buffer.from(tokenSections[0], 'base64').toString(
			'utf8',
		);
		const header = JSON.parse(headerJSON) as TokenHeader;
		const keys = await getPublicKeys();
		const key = keys[header.kid];
		if (key === undefined) {
			throw new Error('claim made for unknown kid');
		}
		const claim = (await verifyPromised(token, key.pem)) as Claim;
		const currentSeconds = Math.floor(new Date().valueOf() / 1000);
		if (currentSeconds > claim.exp || currentSeconds < claim.auth_time) {
			throw new Error('claim is expired or invalid');
		}
		if (claim.iss !== cognitoIssuer) {
			throw new Error('claim issuer is invalid');
		}
		if (claim.token_use !== 'access') {
			throw new Error('claim use is not access');
		}
		console.log(`claim confirmed for ${claim.username}`);
		result = {
			iss: claim.iss,
			sub: claim.sub,
			aud: claim.aud,
			userName: claim.username,
			clientId: claim.client_id,
			isValid: true,
		};
	} catch (error) {
		result = {
			iss: '',
			sub: '',
			aud: '',
			userName: '',
			clientId: '',
			error,
			isValid: false,
		};
	}
	return result;
};

export { decodeToken };

// http://localhost/
// #id_token=eyJraWQiOiJYd29KQURmaWtaUDBTbGxxQ2N4WmFsNU03djU3VEpFUm9lRXVXOHVidUQ4PSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-JSczU179npE9jKTXbY-eKFD2hJEh3ABHVcmA0WzN8jIsPok3NHrD8T32bc4pmUdKWheexc7vCwtttMkPZbiKg63D90e0I74E4iN76WfpZoRIQxthxPfsSsjCtZjLneYnli2C8j7-QDIFFbk3vYHNoMKgjy6yeueKtY4WE07fDJqs8FCteq5KICVAKEkkQM9gG-aWYSODPcVAjUmrIDp4d3mnFHwpG3cHX7Ek0zOc7rJAI-MNedGIDH6JaQ2JOjgoQpgs2VKUhwZJ0iJgXnOWebbqbAMeKLvzAmVqQm-3I7FwRT2HOFVo9igYXzJ6VoBo1E2uAuJqlFEN4nonC-AA
// &access_token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
// &expires_in=3600
// &token_type=Bearer

// http://localhost/
// #access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
// &id_token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
// &token_type=Bearer
// &expires_in=3600
