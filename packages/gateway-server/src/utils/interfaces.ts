import { JwtPayload } from 'jsonwebtoken';

interface ParsedTokenResponse {
	userId: string | null;
	client: string | Array<string> | null;
	issuer: string;
}

// IDP configuration properties for instance
interface TokenIssuerMapping {
	issuer: string;
	validateConsent: boolean;
	parseFunction: {
		(
			token: JwtPayload,
			mappedIssuer: TokenIssuerMapping,
		): ParsedTokenResponse;
	};
}

// Issuer to instance properties map
export interface TokenIssuerMap {
	[issuer: string]: TokenIssuerMapping;
}
