import { User } from '../utils/interfaces';

export const mapDBUserFields = (user: User) => {
	const userKeys = {
		userId: 'user_id',
		email: 'email',
		acceptedTC: 'accepted_tc',
		userType: 'user_type',
		// Client
		firstName: 'first_name',
		lastName: 'last_name',
		// Company
		companyName: 'company_name',
		representativeFirstName: 'representative_first_name',
		representativeLastName: 'representative_last_name',
		// Common
		phone: 'phone',
		address: 'address',
		coordinates: 'coordinates',
		administrativeArea: 'administrative_area',
	};

	const userFields = [];
	Object.keys(userKeys).forEach(
		(key) =>
			user[key] !== null &&
			user[key] !== undefined &&
			userFields.push(userKeys[key]),
	);
	return userFields.join(', ');
};

export const updateDBUserMapping = {
	acceptedTC: 'accepted_tc',
	userType: 'user_type',
	// Client
	firstName: 'first_name',
	lastName: 'last_name',
	// Company
	companyName: 'company_name',
	representativeFirstName: 'representative_first_name',
	representativeLastName: 'representative_last_name',
	stripeCustomerId: 'stripe_customer_id',
	country: 'country',
	city: 'city',
	street: 'street',
	streetNumber: 'street_number',
	postalCode: 'postal_code',
	state: 'state',
	taxId: 'tax_id',
	// Common
	phone: 'phone',
	address: 'address',
	coordinates: 'coordinates',
	administrativeArea: 'administrative_area',
	subscriptionType: 'subscription_type',
};

export const mapUpdateDBUserFields = (user) => {
	const userFields = [];
	Object.keys(updateDBUserMapping).forEach((key) => {
		user[key] !== null &&
			user[key] !== undefined &&
			userFields.push(
				`${updateDBUserMapping[key]}=$${userFields.length + 2}`,
			); // we're starting from 2 because the userId has $1 value
	});

	return userFields;
};

export const createValueIds = (values) => {
	let ids = '';
	for (let i = 0; i < values.length; i++)
		ids += i === 0 ? '$1' : `, $${i + 1}`;
	return ids;
};
