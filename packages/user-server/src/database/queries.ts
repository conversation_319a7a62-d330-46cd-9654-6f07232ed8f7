export const GET_USER_BY_ID =
	'SELECT * FROM customereg."Users" WHERE user_id=$1';

export const ADD_USER = (queryFields, valueIds) =>
	`INSERT INTO customereg."Users" (${queryFields}) VALUES (${valueIds});`;

export const UPDATE_USER = (queryFields) =>
	`UPDATE customereg."Users" SET ${queryFields} WHERE user_id=$1`;

export const DELETE_USER = 'DELETE FROM customereg."Users" WHERE user_id=$1';

export const CHECK_USER = 'SELECT * FROM customereg."Users" WHERE email=$1';

export const ADD_CLIENT_VEHICLE = (queryFields, valueIds) =>
	`INSERT INTO customereg."ClientVehicles" (${queryFields}) VALUES (${valueIds});`;
