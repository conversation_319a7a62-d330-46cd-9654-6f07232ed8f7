export const GET_USER_BY_ID =
	'SELECT * FROM customereg."Users" WHERE user_id=$1';

export const ADD_USER = (queryFields, valueIds) =>
	`INSERT INTO customereg."Users" (${queryFields}) VALUES (${valueIds});`;

export const UPDATE_USER = (queryFields) =>
	`UPDATE customereg."Users" SET ${queryFields} WHERE user_id=$1`;

export const DELETE_USER = 'DELETE FROM customereg."Users" WHERE user_id=$1';

export const CHECK_USER = 'SELECT * FROM customereg."Users" WHERE email=$1';

export const ADD_CLIENT_VEHICLE = (queryFields, valueIds) =>
	`INSERT INTO customereg."ClientVehicles" (${queryFields}) VALUES (${valueIds});`;

export const UPDATE_CLIENT_VEHICLE = (queryFields) =>
	`UPDATE customereg."ClientVehicles" SET ${queryFields}, updated_at=CURRENT_TIMESTAMP WHERE vehicle_id=$1`;

export const GET_CLIENT_VEHICLE_BY_ID =
	'SELECT user_id FROM customereg."ClientVehicles" WHERE vehicle_id=$1';
