import dotenv from 'dotenv';
dotenv.config();

const requiredEnvVars = [
	'NODE_ENV',
	'APOLLO_INTERNAL_USER_ID',
	'POSTGRES_USER',
	'POSTGRES_HOST',
	'POSTGRES_PASSWORD',
	'POSTGRES_PORT',
	'POSTGRES_DATABASE',
	'POSTGRES_CERTIFICATE',
	'PRIVATE_GATEWAY_SERVER_URL',
	'APOLLO_INTERNAL_SECRET',
	'STRIPE_SECRET_KEY',
];

const missingVars = requiredEnvVars.filter(
	(variable) => process.env[variable] === undefined,
);

if (missingVars.length > 0) {
	console.log('Missing env variables: ', missingVars);
}

const {
	NODE_ENV,
	APOLLO_INTERNAL_USER_ID,
	POSTGRES_USER,
	POSTGRES_HOST,
	POSTGRES_PASSWORD,
	POSTGRES_PORT = '5432',
	POSTGRES_DATABASE,
	POSTGRES_CERTIFICATE,
	PRIVATE_GATEWAY_SERVER_URL,
	APOLLO_INTERNAL_SECRET,
	STRIPE_SECRET_KEY,
} = process.env;

export default {
	NODE_ENV,
	APOLLO_INTERNAL_USER_ID,
	POSTGRES_USER,
	POSTGRES_HOST,
	POSTGRES_PASSWORD,
	POSTGRES_PORT,
	POSTGRES_DATABASE,
	POSTGRES_CERTIFICATE,
	PRIVATE_GATEWAY_SERVER_URL,
	APOLLO_INTERNAL_SECRET,
	STRIPE_SECRET_KEY,
};
