import dbClient from '../../../database/db.config';
import { mapUserToResponse } from '../../../utils/mappers';
import { User, UserSubscriptionDetails } from '../../../utils/interfaces';
import {
	createValueIds,
	mapDBUserFields,
	mapUpdateDBUserFields,
	updateDBUserMapping,
} from '../../../database/db.helpers';
import {
	ADD_USER,
	GET_USER_BY_ID,
	UPDATE_USER,
	DELETE_USER,
	CHECK_USER,
} from '../../../database/queries';
import env from '../../../env';
const stripe = require('stripe')(env.STRIPE_SECRET_KEY);

export const getUserDB = async (userId: string) => {
	try {
		const values = [userId];

		const result = await dbClient.query(GET_USER_BY_ID, values);

		const user = result.rows.length && mapUserToResponse(result.rows[0]);

		return user;
	} catch (err) {
		console.log('Error getting user from DB', err.message);
		throw err;
	}
};

export const addUserDB = async (user: User) => {
	try {
		user.acceptedTC = false;

		const queryFields = mapDBUserFields(user);

		const values = [...Object.values(user)];

		const valueIds = createValueIds(values);

		await dbClient.query(ADD_USER(queryFields, valueIds), values);

		return { status: 201, message: 'User added successfully' };
	} catch (err) {
		console.log('Error putting user in DB', err.message);
		throw err;
	}
};

export const updateUserDB = async (user: User) => {
	try {
		const { userId, ...newUser } = user;
		console.log(
			'Updating user in DB with new details: ',
			JSON.stringify(newUser),
		);
		if (newUser.subscriptionType) {
			const userInfo = await getUserDB(userId);

			const customers = await stripe.customers.list({
				email: userInfo.email,
			});
			const customer = customers?.data?.[0];
			if (customer) {
				newUser.stripeCustomerId = customer.id;
			}
		}
		const queryFields = mapUpdateDBUserFields(newUser);
		console.log(queryFields);
		const values = [
			userId,
			...queryFields.map(
				(field) =>
					newUser[
						Object.keys(updateDBUserMapping).find(
							(key) =>
								updateDBUserMapping[key] ===
								field.split('=')[0],
						)
					],
			),
		];

		console.log('values', values);

		console.log('update user', UPDATE_USER(queryFields));

		await dbClient.query(UPDATE_USER(queryFields), values);
		console.log('db updated');

		return { status: 204, message: 'User updated successfully' };
	} catch (err) {
		console.log('Error updating user in DB', err.message);
		throw err;
	}
};

export const deleteUserDB = async (userId: string) => {
	try {
		await dbClient.query(DELETE_USER, [userId]);
		// TODO:
		// cascade and remove everything associated with the userId
		return {
			status: 200,
			message: 'User and corresponding records deleted successfully',
		};
	} catch (err) {
		console.log('Error deleting user from DB', err.message);
		throw err;
	}
};

export const checkUserDb = async (email: string) => {
	try {
		const result = await dbClient.query(CHECK_USER, [email]);

		return !!result.rows.length;
	} catch (err) {
		console.log('Error deleting user from DB', err.message);
		throw err;
	}
};

export const subscribeUser = async (
	userDetails: UserSubscriptionDetails,
	priceId: string,
) => {
	try {
		const userInfo = await getUserDB(userDetails.userId);

		const customers = await stripe.customers.list({
			email: userInfo.email,
		});
		let customer = customers.data[0];
		if (customer) {
			console.log('Customer already exists: ', customers.data[0]);
		} else {
			customer = await stripe.customers.create({
				email: userInfo.email,
				name: userDetails.companyName,
				shipping: {
					address: {
						city: userDetails.city,
						country: userDetails.country,
						line1: `${userDetails.street} ${userDetails.streetNumber}`,
						postal_code: userDetails.postalCode,
						state: userDetails.state,
					},
					name: userDetails.companyName,
				},
				address: {
					city: userDetails.city,
					country: userDetails.country,
					line1: `${userDetails.street} ${userDetails.streetNumber}`,
					postal_code: userDetails.postalCode,
					state: userDetails.state,
				},
				tax_id_data: [{ type: 'ro_tin', value: userInfo.taxId }],
			});
		}

		const subscription = await stripe.subscriptions.create({
			customer: customer.id,
			items: [
				{
					price: priceId,
				},
			],
			payment_behavior: 'default_incomplete',
			payment_settings: {
				save_default_payment_method: 'on_subscription',
			},
			expand: ['latest_invoice.payment_intent'],
		});
		console.log('Subscription created: ', subscription);
		return {
			status: 200,
			message: 'Subscription created successfully',
			subscriptionId: subscription.id,
			clientSecret:
				subscription.latest_invoice.payment_intent.client_secret,
			amount: subscription.latest_invoice.payment_intent.amount,
			currency: subscription.latest_invoice.payment_intent.currency,
		};
	} catch (err) {
		return { status: 400, message: `Error creating subscription: ${err}` };
	}
};
