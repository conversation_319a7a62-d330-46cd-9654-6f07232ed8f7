import { userAuthorised, isInternal } from '../../../utils/auth';

import {
	addUserDB,
	deleteUserDB,
	getUserDB,
	subscribeUser,
	updateUserDB,
} from './user.functions';

const userResolver = {
	Query: {
		getUser: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				console.log('Fetching user...', args.userId);
				try {
					const response = await getUserDB(args.userId);

					return {
						status: 200,
						message: 'User retrieved successfully',
						user: response,
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},

	Mutation: {
		addUser: async (_, args, ctx) => {
			const { userId } = args.user;
			if (userAuthorised(userId, ctx.userId) || isInternal(ctx.userId)) {
				console.log('Adding user...', userId);
				const response = await addUserDB(args.user);
				return response;
			}
			throw new Error('UNAUTHORIZED');
		},
		updateUser: async (_, args, ctx) => {
			const { userId } = args.user;
			if (userAuthorised(userId, ctx.userId) || isInternal(ctx.userId)) {
				try {
					console.log('Updating user...', userId);
					if (args.user) {
						const response = await updateUserDB(args.user);
						return response;
					}
					return {
						message: 'No properties were updated',
						status: 204,
					};
				} catch (err) {
					return {
						message: 'Error updating user',
						status: 500,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
		updateUserInternal: async (_, args, ctx) => {
			const { userId } = args.user;

			if (isInternal(ctx.userId)) {
				try {
					console.log('Updating user...', userId);
					const response = await updateUserDB(args.user);
					return response;
				} catch (err) {
					return {
						message: 'Error updating user',
						status: 500,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
		removeUser: async (_, args, ctx) => {
			if (
				userAuthorised(args.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				const { userId } = args;
				console.log('Deleting user...', args.userId);

				try {
					await deleteUserDB(userId);

					return {
						status: 200,
						message: 'User deleted successfully',
					};
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
		subscribeUser: async (_, args, ctx) => {
			console.log('args user', args.userDetails);
			if (
				userAuthorised(args.userDetails.userId, ctx.userId) ||
				isInternal(ctx.userId)
			) {
				const { userDetails, priceId } = args;
				console.log(
					`Subscribing user ${userDetails.userId} to priceID ${priceId}`,
				);

				try {
					const response = await subscribeUser(userDetails, priceId);
					return response;
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},
};

exports.resolver = userResolver;
