import { randomUUID } from 'crypto';
import dbClient from '../../../database/db.config';
import { ClientVehicle, UpdateClientVehicle } from '../../../utils/interfaces';
import {
	createValueIds,
	mapDBClientVehicleFields,
	extractClientVehicleValues,
	mapUpdateDBClientVehicleFields,
	updateDBClientVehicleMapping,
} from '../../../database/db.helpers';
import {
	ADD_CLIENT_VEHICLE,
	UPDATE_CLIENT_VEHICLE,
	GET_CLIENT_VEHICLE_BY_ID,
} from '../../../database/queries';

export const addClientVehicleDB = async (vehicle: ClientVehicle) => {
	try {
		// Generate a unique vehicle ID
		vehicle.vehicleId = randomUUID();

		const queryFields = mapDBClientVehicleFields(vehicle);

		// Extract values in the same order as the fields
		const values = extractClientVehicleValues(vehicle);

		const valueIds = createValueIds(values);
		console.log('valueIds', valueIds);
		console.log('values', values);

		await dbClient.query(ADD_CLIENT_VEHICLE(queryFields, valueIds), values);

		return {
			status: 201,
			message: 'Vehicle added successfully',
			vehicleId: vehicle.vehicleId,
		};
	} catch (err) {
		console.log('Error adding vehicle to DB', err.message);
		throw err;
	}
};

export const getClientVehicleOwnerDB = async (vehicleId: string) => {
	try {
		const result = await dbClient.query(GET_CLIENT_VEHICLE_BY_ID, [
			vehicleId,
		]);

		if (result.rows.length === 0) {
			return null; // Vehicle not found
		}

		return result.rows[0].user_id;
	} catch (err) {
		console.log('Error getting vehicle owner from DB', err.message);
		throw err;
	}
};

export const updateClientVehicleDB = async (vehicle: UpdateClientVehicle) => {
	try {
		const queryFields = mapUpdateDBClientVehicleFields(vehicle);

		if (queryFields.length === 0) {
			return {
				status: 204,
				message: 'No properties were updated',
			};
		}

		// Extract values for the fields that are being updated
		const values = [vehicle.vehicleId]; // vehicleId is always first for WHERE clause
		Object.keys(updateDBClientVehicleMapping).forEach((key) => {
			if (vehicle[key] !== null && vehicle[key] !== undefined) {
				values.push(vehicle[key]);
			}
		});

		console.log(
			'update vehicle query:',
			UPDATE_CLIENT_VEHICLE(queryFields.join(', ')),
		);
		console.log('update vehicle values:', values);

		await dbClient.query(
			UPDATE_CLIENT_VEHICLE(queryFields.join(', ')),
			values,
		);

		return { status: 204, message: 'Vehicle updated successfully' };
	} catch (err) {
		console.log('Error updating vehicle in DB', err.message);
		throw err;
	}
};
