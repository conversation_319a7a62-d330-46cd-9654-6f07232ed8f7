import { randomUUID } from 'crypto';
import dbClient from '../../../database/db.config';
import { ClientVehicle } from '../../../utils/interfaces';
import {
	createValueIds,
	mapDBClientVehicleFields,
	extractClientVehicleValues,
} from '../../../database/db.helpers';
import { ADD_CLIENT_VEHICLE } from '../../../database/queries';

export const addClientVehicleDB = async (vehicle: ClientVehicle) => {
	try {
		// Generate a unique vehicle ID
		vehicle.vehicleId = randomUUID();

		const queryFields = mapDBClientVehicleFields(vehicle);

		// Extract values in the same order as the fields
		const values = extractClientVehicleValues(vehicle);

		const valueIds = createValueIds(values);
		console.log('valueIds', valueIds);
		console.log('values', values);

		await dbClient.query(ADD_CLIENT_VEHICLE(queryFields, valueIds), values);

		return {
			status: 201,
			message: 'Vehicle added successfully',
			vehicleId: vehicle.vehicleId,
		};
	} catch (err) {
		console.log('Error adding vehicle to DB', err.message);
		throw err;
	}
};
