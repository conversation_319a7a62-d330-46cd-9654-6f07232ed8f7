import { randomUUID } from 'crypto';
import dbClient from '../../../database/db.config';
import { ClientVehicle } from '../../../utils/interfaces';
import {
	createValueIds,
	mapDBClientVehicleFields,
} from '../../../database/db.helpers';
import { ADD_CLIENT_VEHICLE } from '../../../database/queries';

export const addClientVehicleDB = async (vehicle: ClientVehicle) => {
	try {
		// Generate a unique vehicle ID
		vehicle.vehicleId = randomUUID();

		const queryFields = mapDBClientVehicleFields(vehicle);

		const values = [
			...Object.values(vehicle).filter(
				(vehicleField) =>
					vehicleField !== null && vehicleField !== undefined,
			),
		];

		const valueIds = createValueIds(values);

		await dbClient.query(ADD_CLIENT_VEHICLE(queryFields, valueIds), values);

		return { 
			status: 201, 
			message: 'Vehicle added successfully',
			vehicleId: vehicle.vehicleId 
		};
	} catch (err) {
		console.log('Error adding vehicle to DB', err.message);
		throw err;
	}
};
