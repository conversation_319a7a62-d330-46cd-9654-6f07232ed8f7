import { userAuthorised, isInternal } from '../../../utils/auth';
import { addClientVehicleDB } from './vehicle.functions';

const vehicleResolver = {
	Query: {},

	Mutation: {
		addClientVehicle: async (_, args, ctx) => {
			const { userId } = args.vehicle;
			if (userAuthorised(userId, ctx.userId) || isInternal(ctx.userId)) {
				console.log('Adding client vehicle...', userId);
				try {
					const response = await addClientVehicleDB(args.vehicle);
					return response;
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
	},
};

exports.resolver = vehicleResolver;
