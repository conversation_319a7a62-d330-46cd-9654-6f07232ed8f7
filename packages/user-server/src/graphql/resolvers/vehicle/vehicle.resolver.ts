import { userAuthorised, isInternal } from '../../../utils/auth';
import {
	addClientVehicleDB,
	updateClientVehicleDB,
	getClientVehicleOwnerDB,
} from './vehicle.functions';

const vehicleResolver = {
	Query: {},

	Mutation: {
		addClientVehicle: async (_, args, ctx) => {
			const { userId } = args.vehicle;
			if (userAuthorised(userId, ctx.userId) || isInternal(ctx.userId)) {
				console.log('Adding client vehicle...', userId);
				try {
					const response = await addClientVehicleDB(args.vehicle);
					return response;
				} catch (err) {
					return {
						status: 500,
						message: err.message,
					};
				}
			}
			throw new Error('UNAUTHORIZED');
		},
		updateClientVehicle: async (_, args, ctx) => {
			console.log('Updating client vehicle...', args.vehicle.vehicleId);

			try {
				// First, get the vehicle owner to check authorization
				const vehicleOwnerId = await getClientVehicleOwnerDB(
					args.vehicle.vehicleId,
				);

				if (!vehicleOwnerId) {
					return {
						status: 404,
						message: 'Vehicle not found',
					};
				}

				// Check if user is authorized to update this vehicle
				if (
					userAuthorised(vehicleOwnerId, ctx.userId) ||
					isInternal(ctx.userId)
				) {
					const response = await updateClientVehicleDB(args.vehicle);
					return response;
				} else {
					throw new Error('UNAUTHORIZED');
				}
			} catch (err) {
				if (err.message === 'UNAUTHORIZED') {
					throw err;
				}
				return {
					status: 500,
					message: err.message,
				};
			}
		},
	},
};

exports.resolver = vehicleResolver;
