input ClientVehicleInput {
	userId: String!
	vehicleType: String!
	makeId: String
	makeName: String
	model: String!
	year: Int!
	engine: String
	registrationNumber: String!
}

input UpdateClientVehicleInput {
	vehicleId: String!
	vehicleType: String
	makeId: String
	makeName: String
	model: String
	year: Int
	engine: String
	registrationNumber: String
}

type ClientVehicle {
	vehicleId: String
	userId: String
	vehicleType: String
	makeId: String
	makeName: String
	model: String
	year: Int
	engine: String
	registrationNumber: String
	createdAt: String
	updatedAt: String
}
