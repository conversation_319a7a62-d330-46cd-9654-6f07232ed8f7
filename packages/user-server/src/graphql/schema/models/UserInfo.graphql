input UserInfoInput {
	userId: String!
	email: String!
}

type LocationCoordinates {
	lat: String
	lng: String
}

input LocationCoordinatesInput {
	lat: String
	lng: String
}

input UpdateUserInfoInternal {
	userId: String!
	email: String
	acceptedTC: Boolean
	userType: String
	# Client
	firstName: String
	lastName: String

	# Company
	companyName: String
	representativeFirstName: String
	representativeLastName: String

	# Common
	phone: String
	address: String
	coordinates: LocationCoordinatesInput
}

input UpdateUserInfoExternal {
	userId: String!
	acceptedTC: Boolean
	userType: String
	# Client
	firstName: String
	lastName: String

	# Company
	companyName: String
	representativeFirstName: String
	representativeLastName: String
	subscriptionType: String
	country: String
	city: String
	street: String
	streetNumber: String
	postalCode: String
	state: String
	taxId: String

	# Common
	phone: String
	address: String
	coordinates: LocationCoordinatesInput
	administrativeArea: String
}

type UserInfo {
	userId: String
	email: String
	acceptedTC: Boolean
	userType: String
	# Client
	firstName: String
	lastName: String

	# Company
	companyName: String
	representativeFirstName: String
	representativeLastName: String
	subscriptionType: String
	country: String
	city: String
	street: String
	streetNumber: String
	postalCode: String
	state: String
	taxId: String

	# Common
	phone: String
	address: String
	coordinates: LocationCoordinates
	administrativeArea: String
}
