type SubscribeUserResponse {
	status: Int
	message: String
	subscriptionId: String
	clientSecret: String
	amount: Int
	currency: String
}

input SubscriptionUserDetails {
	userId: String
	country: String
	city: String
	street: String
	streetNumber: String
	postalCode: String
	state: String
	representativeFirstName: String
	representativeLastName: String
	companyName: String
}

type Mutation {
	subscribeUser(
		userDetails: SubscriptionUserDetails
		priceId: String
	): SubscribeUserResponse
}
