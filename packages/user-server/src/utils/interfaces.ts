export interface User {
	userId: string;
	email: string;
	acceptedTC: boolean;
	userType: string;
	// Client
	firstName: string;
	lastName: string;
	// Company
	companyName: string;
	representativeFirstName: string;
	representativeLastName: string;
	subscriptionType?: SubscriptionType;
	stripeCustomerId?: string;
	country: string;
	city: string;
	street: string;
	streetNumber: string;
	postalCode: string;
	state: string;
	taxId: string;
	// Common
	phone: string;
	address: string;
	coordinates: { lat: number; lng: number };
	administrativeArea: string;
}

export interface UserDB {
	user_id: string;
	email: string;
	accepted_tc: boolean;
	user_type: string;
	// Client
	first_name: string;
	last_name: string;
	// Company
	company_name: string;
	representative_first_name: string;
	representative_last_name: string;
	stripe_customer_id?: string;
	subscription_type?: SubscriptionType;
	country: string;
	city: string;
	street: string;
	street_number: string;
	postal_code: string;
	state: string;
	tax_id: string;
	// Common
	phone: string;
	address: string;
	coordinates: { lat: number; lng: number };
	administrative_area: string;
}

export interface UserSubscriptionDetails {
	userId: string;
	country: string;
	city: string;
	street: string;
	streetNumber: string;
	postalCode: string;
	state: string;
	representativeFirstName: string;
	representativeLastName: string;
	companyName: string;
}

export enum SubscriptionType {
	PRO = 'PRO',
	BASIC = 'BASIC',
}
