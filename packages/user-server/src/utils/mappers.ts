import { User, UserDB } from './interfaces';

export const mapUserToResponse = (user: UserDB): User => ({
	userId: user.user_id,
	email: user.email,
	acceptedTC: user?.accepted_tc,
	userType: user?.user_type,
	// Client
	firstName: user?.first_name,
	lastName: user?.last_name,
	// Company
	companyName: user?.company_name,
	representativeFirstName: user?.representative_first_name,
	representativeLastName: user?.representative_last_name,
	subscriptionType: user?.subscription_type,
	country: user?.country,
	city: user?.city,
	street: user?.street,
	streetNumber: user?.street_number,
	postalCode: user?.postal_code,
	state: user?.state,
	taxId: user?.tax_id,
	// Common
	phone: user?.phone,
	address: user?.address,
	coordinates: user?.coordinates,
	administrativeArea: user?.administrative_area,
});
