version: '3.8'

services:
    ## dynamodb
    dynamodb:
        image: amazon/dynamodb-local
        restart: on-failure
        ports:
            - 8000:8000
        command: '-jar DynamoDBLocal.jar -inMemory -sharedDb'
        networks:
            - easygarage-backend_local-network

    ## postgres
    postgres:
        image: postgres:14.5
        container_name: container-postgres
        volumes:
            - './postgres:/docker-entrypoint-initdb.d'
            - './postgres:/var/lib/postgresql/some-data'
        restart: on-failure
        ports:
            - 5432:5432
        environment:
            - POSTGRES_PASSWORD=123456
        deploy:
            restart_policy:
                condition: on-failure
                max_attempts: 3
        networks:
            - easygarage-backend_local-network

    ## pgadmin
    pgadmin:
        image: dpage/pgadmin4
        container_name: container-pgadmin
        volumes:
            - ./postgres/servers.json:/pgadmin4/servers.json
        restart: on-failure
        ports:
            - 8080:80
        environment:
            - PGADMIN_DEFAULT_EMAIL=<EMAIL>
            - PGADMIN_DEFAULT_PASSWORD=123456
            - PGADMIN_LISTEN_PORT=80
        deploy:
            restart_policy:
                condition: on-failure
                max_attempts: 3
        networks:
            - easygarage-backend_local-network

networks:
    easygarage-backend_local-network:
        external: true
