-- GRANT USAGE ON SCHEMA customerEG TO postgres;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA customerEG TO postgres;
SET search_path TO customerEG;


CREATE TYPE SUBSCRIPTION_TYPE_ENUM AS ENUM('PRO', 'BASIC');

-- Users table
CREATE TABLE customerEG."Users"(user_id VARCHAR(36) PRIMARY KEY, email VARCHAR(50), stripe_customer_id VARCHAR(256), accepted_tc BOOLEAN, user_type VARCHAR(30), first_name <PERSON><PERSON><PERSON><PERSON>(60), last_name <PERSON><PERSON><PERSON><PERSON>(60), company_name <PERSON><PERSON><PERSON><PERSON>(60),representative_first_name <PERSON><PERSON><PERSON><PERSON>(60),representative_last_name <PERSON><PERSON><PERSON><PERSON>(60), phone VARCHAR(11), address VARCHAR(255), coordinates JSONB, administrative_area VARCHAR(255), subscription_type SUBSCRIPTION_TYPE_ENUM, country VARCHAR(255), city VARCHAR(255), street VARCHAR(255), street_number VARCHAR(255),  postal_code VA<PERSON>HA<PERSON>(255), state VARCHAR(255), tax_id VARCHAR(255));

-- Service Types table
CREATE TABLE customerEG."ServiceTypes"(service_type_id VARCHAR(36) PRIMARY KEY, name VARCHAR(255));

-- Service Types table
CREATE TABLE customerEG."SubserviceTypes"(subservice_type_id VARCHAR(36) PRIMARY KEY, service_type_id VARCHAR(36) REFERENCES customerEG."ServiceTypes"(service_type_id), name VARCHAR(255));

-- Services table
CREATE TABLE customerEG."Services"(service_id VARCHAR(36) PRIMARY KEY, user_id VARCHAR(36) REFERENCES customerEG."Users"(user_id), service_type_id VARCHAR(36) REFERENCES customerEG."ServiceTypes"(service_type_id), start_hour TIME, end_hour TIME, parallel_appointment_capacity INT, allowed_car_makes VARCHAR(60)[], subservice_type_ids VARCHAR(36)[], price INT);

-- Car makes table
CREATE TABLE customerEG."CarMakes"(id VARCHAR(36) PRIMARY KEY, name VARCHAR(255));

-- Vehicle types enum
CREATE TYPE VEHICLE_TYPE_ENUM AS ENUM('CAR', 'MOTORCYCLE', 'TRUCK', 'VAN', 'SUV', 'COUPE', 'SEDAN', 'HATCHBACK', 'CONVERTIBLE', 'WAGON', 'PICKUP', 'BUS', 'OTHER');

-- Client vehicles table
CREATE TABLE customerEG."ClientVehicles"(
    vehicle_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES customerEG."Users"(user_id) ON DELETE CASCADE,
    vehicle_type VEHICLE_TYPE_ENUM NOT NULL,
    make_id VARCHAR(36) REFERENCES customerEG."CarMakes"(id),
    make_name VARCHAR(100), -- For cases where make is not in CarMakes table
    model VARCHAR(100) NOT NULL,
    year INTEGER CHECK (year >= 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 2),
    engine VARCHAR(100), -- Engine type/size (e.g., "2.0L Turbo", "Electric", "1.6L Diesel")
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_make_reference CHECK (
        (make_id IS NOT NULL AND make_name IS NULL) OR
        (make_id IS NULL AND make_name IS NOT NULL)
    )
);

