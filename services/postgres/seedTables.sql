GRANT USAGE ON SCHEMA customerEG TO postgres;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA customerEG TO postgres;
SET search_path TO customerEG;

INSERT INTO "Users" (user_id, email, accepted_tc, user_type, first_name, last_name, phone, address, coordinates) values ('c41d7fc4-a84f-4f11-a6af-50c9c5d106f7', '<PERSON>@mail.com', false, 'CLIENT', '<PERSON>', '<PERSON><PERSON>', '0725033333', 'Str Grivitei 45, <PERSON><PERSON><PERSON><PERSON>', '{"lng": 23.123, "lat": 45.678}');
INSERT INTO "Users" (user_id, email, stripe_customer_id, accepted_tc, user_type, company_name, representative_first_name, representative_last_name, phone, address, coordinates) values ('c41d7fc4-a84f-4f11-a6af-50c9c5d106f1', '<EMAIL>', 'cs-1234', false, 'COMPANY', 'EuroTech Auto SRL', '<PERSON>', '<PERSON><PERSON>', '0725033333', 'Str Grivitei 41, <PERSON><PERSON><PERSON><PERSON>', '{"lng": 24.123, "lat": 35.678}');


-- Inserarea în ServiceTypes
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('1', 'Vulcanizare');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('2', 'Reparații mecanice');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('3', 'Electricitate auto');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('4', 'Diagnoză auto');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('5', 'Detailing auto');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('6', 'Întreținere și revizii periodice');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('7', 'Service roți și echilibrare');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('8', 'Schimb de ulei și filtre');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('9', 'Vopsitorie și tinichigerie');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('10', 'Înlocuire parbriz și geamuri');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('11', 'Reparații AC și climatizare');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('12', 'Tuning auto');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('13', 'Service pentru sistemul de frânare');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('14', 'Reparare sistem de evacuare');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('15', 'Geometrie roți (aliniere)');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('16', 'Întreținere și reparații ale sistemului de direcție și suspensii');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('17', 'Repararea sau înlocuirea bateriei');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('18', 'Închiriere auto și transport auto');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('19', 'Repararea și întreținerea sistemelor hibride și electrice');
INSERT INTO customerEG."ServiceTypes" (service_type_id, name) VALUES ('20', 'Inspecție Tehnică Periodică (ITP)');


-- Inserarea în SubserviceTypes
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('1', '1', 'Montare anvelope');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('2', '1', 'Reparare pană');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('3', '2', 'Reparații motor');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('4', '2', 'Reparații suspensii');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('5', '3', 'Instalare baterie');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('6', '3', 'Reparare faruri');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('7', '4', 'Diagnoză motor');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('8', '4', 'Diagnoză electronică');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('9', '5', 'Curățare interior');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('10', '5', 'Curățare exterior');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('11', '6', 'Schimb ulei motor');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('12', '6', 'Schimb filtru aer');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('13', '7', 'Echilibrare roți față');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('14', '7', 'Echilibrare roți spate');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('15', '8', 'Schimb filtru combustibil');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('16', '8', 'Schimb filtru polen');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('17', '9', 'Vopsire completă');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('18', '9', 'Reparații tinichigerie');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('19', '10', 'Înlocuire parbriz față');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('20', '10', 'Înlocuire geam lateral');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('21', '11', 'Încărcare freon');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('22', '11', 'Reparare AC');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('23', '12', 'Chiptuning');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('24', '12', 'Body kit');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('25', '13', 'Schimb plăcuțe frână');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('26', '13', 'Schimb discuri frână');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('27', '14', 'Reparare toba de eșapament');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('28', '14', 'Înlocuire catalizator');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('29', '15', 'Aliniere față');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('30', '15', 'Aliniere spate');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('31', '16', 'Înlocuire bucșe');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('32', '16', 'Reparație casetă direcție');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('33', '17', 'Schimbare baterie auto');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('34', '17', 'Întreținere baterie');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('35', '18', 'Transport platformă');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('36', '18', 'Închiriere mașină pe termen scurt');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('37', '19', 'Reparare sistem de încărcare hibrid');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('38', '19', 'Întreținere sistem electric');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('39', '20', 'ITP pentru autoturisme');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('40', '20', 'ITP pentru autoutilitare');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('41', '20', 'ITP pentru motociclete');
INSERT INTO customerEG."SubserviceTypes" (subservice_type_id, service_type_id, name) VALUES ('42', '20', 'ITP pentru vehicule comerciale');


INSERT INTO customerEG."CarMakes" (id, name) VALUES (1, 'Abarth');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (2, 'Acura');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (3, 'Aiways');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (4, 'Aixam');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (5, 'Alfa');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (6, 'Allview');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (7, 'Aro');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (8, 'Aston');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (9, 'Audi');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (10, 'Austin');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (11, 'Automobiles');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (12, 'BMW');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (13, 'Baic');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (14, 'Bentley');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (15, 'Bronco');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (16, 'Bugatti');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (17, 'Buick');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (18, 'Byd');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (19, 'Cadillac');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (20, 'Chatenet');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (21, 'Chevrolet');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (22, 'Chrysler');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (23, 'Citroën');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (24, 'Comarth');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (25, 'Cupra');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (26, 'DFSK');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (27, 'DKW');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (28, 'DR');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (29, 'DS');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (30, 'Dacia');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (31, 'Daewoo');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (32, 'Daihatsu');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (33, 'Dodge');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (34, 'Eagle');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (35, 'Excalibur');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (36, 'Ferrari');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (37, 'Fiat');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (38, 'Ford');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (39, 'Forthing');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (40, 'GMC');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (41, 'Geely');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (42, 'Genesis');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (43, 'Gonow');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (44, 'Grecav');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (45, 'Holden');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (46, 'Honda');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (47, 'Hummer');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (48, 'Hyundai');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (49, 'Ineos');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (50, 'Infiniti');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (51, 'Isuzu');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (52, 'Iveco');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (53, 'JAC');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (54, 'Jaguar');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (55, 'Jeep');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (56, 'KG');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (57, 'KTM');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (58, 'Kaipan');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (59, 'Kia');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (60, 'Koenigsegg');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (61, 'Lada');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (62, 'Lamborghini');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (63, 'Lancia');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (64, 'Land');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (65, 'Leapmotor');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (66, 'Lexus');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (67, 'Ligier');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (68, 'Lincoln');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (69, 'Lomax');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (70, 'Lotus');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (71, 'LuAZ');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (72, 'Lucid');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (73, 'Lynk&amp');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (74, 'MG');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (75, 'MPM');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (76, 'Mahindra');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (77, 'Martin');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (78, 'Maruti');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (79, 'Maserati');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (80, 'Maxus');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (81, 'Maybach');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (82, 'Mazda');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (83, 'McLaren');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (84, 'Mercedes-Benz');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (85, 'Mercury');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (86, 'Microcar');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (87, 'Microlino');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (88, 'Mini');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (89, 'Mitsubishi');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (90, 'Mobility');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (91, 'Morgan');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (92, 'Motors');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (93, 'NSU');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (94, 'Nio');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (95, 'Nissan');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (96, 'Nysa');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (97, 'Opel');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (98, 'Peugeot');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (99, 'Plymouth');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (100, 'Polestar');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (101, 'Polonez');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (102, 'Pontiac');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (103, 'Porsche');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (104, 'Proton');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (105, 'Renault');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (106, 'Rivian');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (107, 'Rolls-Royce');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (108, 'Romeo');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (109, 'Rover');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (110, 'SWM');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (111, 'Saab');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (112, 'Samsung');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (113, 'Saturn');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (114, 'Seat');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (115, 'Seres');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (116, 'Skoda');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (117, 'Skywell');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (118, 'Smart');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (119, 'Sport');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (120, 'SsangYong');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (121, 'Subaru');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (122, 'Suzuki');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (123, 'Syrena');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (124, 'TVR');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (125, 'Tarpan');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (126, 'Tata');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (127, 'Tatra');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (128, 'Tavria');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (129, 'Tazzari');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (130, 'Tesla');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (131, 'Toyota');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (132, 'Trabant');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (133, 'Triumph');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (134, 'Vauxhall');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (135, 'Volkswagen');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (136, 'Volvo');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (137, 'Warszawa');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (138, 'X');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (139, 'Xev');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (140, 'Xpeng');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (141, 'Yugo');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (142, 'Zaporożec');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (143, 'Zastawa');
INSERT INTO customerEG."CarMakes" (id, name) VALUES (144, 'Zeekr');
